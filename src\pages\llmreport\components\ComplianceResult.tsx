
import { Typography, Divider, Tabs } from 'antd';
import {
    ProCard
} from '@ant-design/pro-components';
import TestResultCompt from './TestResultCompt';
import { useEffect, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import RemarkGfm from 'remark-gfm';
import PassingRateStatisticComptPanel from './PassingRateStatisticComptPanel';
import EvaluationTable from './EvaluationTable';
import { BarChartOutlined, AreaChartOutlined } from '@ant-design/icons';
import { getTaskReportSummaryDetail } from "@/servers/report";

const { Title, Paragraph, Text } = Typography;

const preStyle = {
    whiteSpace: 'pre-wrap', wordBreak: 'break-all',
    fontFamily: 'PingFang SC, PingFang SC-Regular',
    fontWeight: '400',
    color: '#535353',
    border: 'none',
};

const isEmptyObj = (obj: any) => {
    const isEmpty = Object.keys(obj).length === 0;
    return isEmpty;
}

function SubclassNode({ parentClassification, parent_level, parent_index, classification, taskID }) {
    // console.log("SubclassNode   parentName:",parentName,"   model:",model)
    console.log('parent_level', parent_index, parent_level, classification.name);
    console.log('classification:', classification);

    const [selectTabs, setSelectTabs] = useState('1');
    const onChange = (key: any) => {
        console.log("当前key:", key);
        // const { data, total } = await getTaskTestResult(
        //     {
        //         task_id: taskID,
        //         classification: category?.name,
        //         offset: current,
        //         limit: pageSize,
        //     }
        // )

        setSelectTabs(key);
    }
    return (
        <div>
            {parent_level === 1 && <>
                <Title level={5} type="success"> 3.2.{parent_index}  {classification.name_alias || classification.name}</Title>
                {/* <ul> */}
                {
                    classification.description && <pre style={preStyle}>{classification.description}</pre>
                }
                {
                    classification.detection_principle &&
                    <>
                        <ProCard style={{ backgroundColor: '#CFE6C0', marginBottom: 10 }}>
                            <Text strong>检测方法</Text>
                            <ReactMarkdown
                                remarkPlugins={[RemarkGfm]}
                                // components={{
                                //     code({ node, inline, className, children, ...props }) {
                                //         return !inline && className?.includes('language-')
                                //             ? <pre {...props}><code>{children}</code></pre>
                                //             : <code {...props}>{children}</code>;
                                //     },
                                // }}

                            >
                            {classification.detection_principle}
                            </ReactMarkdown>
                            {/* <blockquote >{classification.detection_principle}</blockquote> */}
                        </ProCard>
                    </>
                }
                <li>
                    {/* <Text strong><FormattedMessage id='test_result' /></Text> */}
                    {/* <ProCard title={(classification.name_alias || classification.name) + ` - 检测汇总`} style={{ marginBottom: 10, marginTop: 5 }} boxShadow>
                        <PassingRateStatisticComptPanel
                            classification={classification}
                            taskID={taskID}
                            qualification_rate_standard={isEmptyObj(classification.qualification_rate_standard) ? parentClassification?.qualification_rate_standard : classification.qualification_rate_standard}
                        />
                    </ProCard> */}
                    <ProCard title={"检测结果汇总"} style={{ marginBottom: 10, marginTop: 5 }} boxShadow>
                        <PassingRateStatisticComptPanel
                            classification={classification}
                            taskID={taskID}
                            qualification_rate_standard={isEmptyObj(classification.qualification_rate_standard) ? parentClassification?.qualification_rate_standard : classification.qualification_rate_standard}
                        />
                    </ProCard>
                </li>

                {/* </ul> */}

            </>}
            {classification.children && (
                // <Tabs
                //     type="card"
                //     defaultActiveKey="1"
                //     size={'large'}
                //     className="custom-compliance-tabs"
                //     activeKey={selectTabs}
                //     onChange={onChange}
                // >
                //     {classification.children.map((child, index) => (
                //         <TabPane
                //             key={String(index + 1)}
                //             tab={child.name_alias || child.name}
                //         >
                //             {/* 这里可以添加每个 Tab 的具体内容 */}
                //             <p>这是 {child.name_alias || child.name} 的内容</p>
                //             {classification.description && <pre style={preStyle}>{classification.description}</pre>}
                //             <li> <Text strong>检测结果</Text>
                //                 {
                //                     classification.show_summary && <>
                //                         {/* <ProCard style={{ marginBottom: 10, marginTop: 5 }} boxShadow > */}
                //                         <PassingRateStatisticCompt
                //                             show_categories_column={classification.show_categories_column}
                //                             classification={classification}
                //                             taskID={taskID}
                //                             subclass={classification.children}
                //                             qualification_rate_standard={isEmptyObj(classification.qualification_rate_standard) ? parentClassification?.qualification_rate_standard : classification.qualification_rate_standard}
                //                         />
                //                         {/* </ProCard> */}
                //                     </>
                //                 }
                //                 <TestResultCompt
                //                     taskID={taskID}
                //                     classification={classification}
                //                     parentClassification={parentClassification}
                //                 />
                //             </li>

                //         </TabPane>
                //     ))}
                // </Tabs>
                <TestResultCompt
                    taskID={taskID}
                    classification={classification}
                    parentClassification={parentClassification}
                />
            )}
        </div>
    );
}

const ComplianceResult = (props: any) => {

    const { taskID, classification } = props
    // console.log("parentNmae:",complianceTemp.name,"  最大的哪个 model:", model)
    const [abstract, setAbstract] = useState('');
    const [mitigation, setMitigation] = useState('');
    const [rateData, setRateData] = useState([]);
    console.log("taskID:", taskID, "complianceTemp:", classification.name)

    const initReportDetail = async () => {
        const { data } = await getTaskReportSummaryDetail({
            task_id: taskID,
            classification_id: classification?.id,
            limit: 0,
            offset: 1
        })
        setRateData(data)
    }
    // 初始化加载
    useEffect(() => {
        initReportDetail()
    }, [])

    return (
        <>
            <div id="section3.1">
                <Paragraph>
                    {/* 描述 */}
                    <pre style={preStyle}>{classification?.description}</pre>

                    <Title
                        level={3}
                        style={{
                            display: 'flex',
                            alignItems: 'center',
                            paddingLeft: '16px', // 给分隔线留出空间
                            position: 'relative'
                        }}
                    >
                        {/* 模拟 ::before 伪元素 */}
                        <span style={{
                            position: 'absolute',
                            left: 0,
                            top: '50%',
                            transform: 'translateY(-50%)',
                            width: '6px',
                            height: '24px',
                            backgroundColor: '#67c23a', // 绿色
                            boxShadow: '0 0 8px rgba(103, 194, 58, 0.5)' // 绿色阴影
                        }}></span>
                        3.1 评估指标
                    </Title>
                    <Divider type={'horizontal'}></Divider>
                    <EvaluationTable   taskID={ taskID} data={rateData}></EvaluationTable>

                    {/* 整体通过率面板 */}
                    {/* {   //五个大类的总测试数和通过拒绝数无法展示，目前找到的原因，下一行代码中的taskID无法准确传递
                    classification.show_summary && <><PassingRateStatisticCompt classification={classification} taskID={taskID} qualification_rate_standard={classification.qualification_rate_standard} /></>

                } */}
                </Paragraph>
            </div>
            <div id="section3.2">
                <Title level={3} style={{
                    display: 'flex',
                    alignItems: 'center',
                    paddingLeft: '16px', // 给分隔线留出空间
                    position: 'relative'
                }}>
                    {/* 模拟 ::before 伪元素 */}
                    <span style={{
                        position: 'absolute',
                        left: 0,
                        top: '50%',
                        transform: 'translateY(-50%)',
                        width: '6px',
                        height: '24px',
                        backgroundColor: '#67c23a', // 绿色
                        boxShadow: '0 0 8px rgba(103, 194, 58, 0.5)' // 绿色阴影
                    }}></span>
                    3.2 评估细节
                </Title>
                <Divider type={'horizontal'}></Divider>
                {

                    classification.children && Object.values(classification.children).map((category, index) => {
                        return <SubclassNode
                            key={classification.name + index}
                            show_test_result={classification?.show_test_result || false}
                            parent_index={index + 1}
                            parent_level={1}
                            taskID={taskID}
                            classification={category}
                            parentClassification={classification}
                        />
                    })
                }
            </div>


        </>
    )
}

export default ComplianceResult;



