import type { EChartsCoreOption } from 'echarts';
import { useEffect } from 'react';
import { useEcharts } from '@/hooks/useEcharts';
import { useCommonStore } from '@/hooks/useCommonStore';
import { t } from 'i18next';

 let result = [
   { name: "22年1月", 事件1: 49, 事件2: 156 },
   { name: "22年2月", 事件1: 92, 事件2: 184 },
   { name: "22年3月", 事件1: 175, 事件2: 248 },
   { name: "22年4月", 事件1: 112, 事件2: 200 },
   { name: "22年5月", 事件1: 74, 事件2: 160 },
   { name: "22年6月", 事件1: 93, 事件2: 207 },
   { name: "22年7月", 事件1: 69, 事件2: 181 },
   { name: "22年8月", 事件1: 22, 事件2: 156 },
 ];
 let keys = ["事件1", '事件2'];
 let seriesData: { name: string; data: any[]; type: string; yAxisIndex: number; symbol: string; symbolSize: number; barWidth: number; splitNumber: number; smooth: boolean; itemStyle: { color: string; }; lineStyle: { color: string; width: number; }; areaStyle: { color: { x: number; y: number; x2: number; y2: number; type: string; global: boolean; colorStops: { offset: number; color: string; }[]; }; }; }[] = [];
 let color = ["95,150,234", "104,185,46"];
 keys.forEach((key, index) => {
   seriesData.push({
     name: key + "数",
     data: result.map((item: any) => {
       return item[key];
     }),
     type: "line",
     yAxisIndex: 0,
     symbol: "none",
     symbolSize: 7,
     barWidth: 14,
     splitNumber: 6,
     smooth: true,
     itemStyle: {
       color: `rgba(${color[index]},1)`,
     },
     lineStyle: {
       color: `rgba(${color[index]},1)`,
       width: 2,
     },
     areaStyle: {
       color: {
         x: 0,
         y: 0,
         x2: 0,
         y2: 1,
         type: "linear",
         global: false,
         colorStops: [
           {
             offset: 0,
             color: `rgba(${color[index]},0.25)`,
           },
           {
             offset: 1,
             color: `rgba(${color[index]},0)`,
           },
         ],
       },
     },
   });
 });
const option: EChartsCoreOption = {
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
  },
  grid: {
    top: "15%",
    left: "2%",
    right: "3%",
    bottom: "2%",
    containLabel: true,
  },
  legend: {
    top: "4%",
    itemWidth: 12,
    itemGap: 16,
    itemHeight: 4,
    icon: "roundRect",
    textStyle: {
      color: "#7E7E7E",
    },
  },
  dataZoom: [
    {
      show: false,
    },
  ],
  xAxis: {
    type: "category",
    boundaryGap: false, // 紧挨边缘
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: "#7E7E7E",
      fontSize: 12,
      margin: 10, // x轴距离文字距离
    },
    data: ["2022-03", "2022-04", "2022-05", "2022-06", "2022-07", "2022-08"],
  },
  yAxis: {
    type: "value",
    splitLine: {
      show: true,
      lineStyle: {
        type: "dashed",
      },
    },
    axisLabel: {
      //y 轴样式
      formatter: "{value}",
      color: "#7E7E7E",
      fontSize: 12,
    },
  },
  series: seriesData,
};

function Line() {
  const { permissions } = useCommonStore();
  const [echartsRef, init] = useEcharts(option);

  useEffect(() => {
    if (permissions.length) {
      setTimeout(() => {
        init();
      }, 100);
    }
  }, [init, permissions.length]);

  return (
    <div className="h-350px">
      <div ref={echartsRef} className="w-full h-full"></div>
    </div>
  );
}

export default Line;