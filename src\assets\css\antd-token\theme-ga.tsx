// AntdUI组件的token配置
const THEME_GA = {
  token: {
    colorSuccess: "#55a722",
    colorWarning: "#fa8c16",
    colorError: "#ec4f4f",
    colorTextBase: "#535353",
    borderRadius: 4,
    colorBgLayout: "#f7f8fa",
    colorBgSpotlight: "#000000bf",
    colorBgMask: "#00000066",
    colorText: "#2e2e2e",
    colorTextSecondary: "#535353",
    colorTextTertiary: "#7e7e7e",
    colorTextQuaternary: "#aaaaaa",
    colorBorder: "#dee0e3",
    colorBorderSecondary: "#ebeef2",
    colorFill: "#dfe0e1",
    colorFillSecondary: "#f7f8fa",
    colorFillTertiary: "#f0f1f3",
    colorFillQuaternary: "#f7f8fa",
  },
  components: {
    Button: {
      borderColorDisabled: "rgb(217, 217, 217)",
      textHoverBg: "rgb(247, 248, 250)",
      colorBgContainerDisabled: "rgb(240, 241, 243)",
      colorText: "rgb(46, 46, 46)",
      colorTextDisabled: "rgb(170, 170, 170)",
      colorErrorActive: "rgb(217, 54, 62)",
      colorErrorBg: "rgb(253, 237, 237)",
    },
    Divider: {
      colorSplit: "rgb(235, 238, 242)",
    },
    Anchor: {
      colorSplit: "rgb(235, 238, 242)",
    },
    Breadcrumb: {
      itemColor: "rgb(126, 126, 126)",
      lastItemColor: "rgb(83, 83, 83)",
      colorBgTextHover: "rgb(247, 248, 250)",
    },
    Dropdown: {
      controlItemBgHover: "rgb(247, 248, 250)",
      colorSplit: "rgb(235, 238, 242)",
    },
    Menu: {
      itemSelectedBg: "#55a722",
      itemSelectedColor: "#ffffff",
      radiusItem: 0,
      itemBorderRadius: 0,
      radiusSubMenuItem: 0,
      subMenuItemBorderRadius: 0,
      darkItemBg: "#26303a",
      darkSubMenuItemBg: "#26303a",
      colorBgContainer: "#26303a",
      colorBgElevated: "#26303a",
      itemHoverColor: "#ffffff",
      itemHoverBg: "#26303a",
      itemColor: "rgba(255,255,255,0.65)",
      marginXXS: "0px",
    },
    Pagination: {
      itemActiveBgDisabled: "rgb(240, 241, 243)",
      itemActiveColorDisabled: "rgb(170, 170, 170)",
      colorBgContainerDisabled: "rgb(240, 241, 243)",
      colorBorder: "rgb(222, 224, 227)",
      wireframe: true,
    },
    Steps: {
      colorSplit: "rgb(235, 238, 242)",
      colorPrimaryBorder: "rgb(22, 119, 255)",
      wireframe: true,
    },
    Cascader: {
      colorSplit: "rgb(235, 238, 242)",
      controlItemBgHover: "rgb(247, 248, 250)",
      colorHighlight: "rgb(241, 164, 93)",
    },
    Checkbox: {
      colorBgContainerDisabled: "rgb(240, 241, 243)",
      colorBorder: "rgb(235, 238, 242)",
      colorTextDisabled: "rgb(170, 170, 170)",
    },
    DatePicker: {
      cellBgDisabled: "rgb(240, 241, 243)",
      cellHoverBg: "rgb(247, 248, 250)",
      colorSplit: "rgb(235, 238, 242)",
      addonBg: "rgb(247, 248, 250)",
    },
    Form: {
      labelColor: "rgb(83, 83, 83)",
      labelRequiredMarkColor: "rgb(236, 79, 79)",
      colorBorder: "rgb(222, 224, 227)",
    },
    Input: {
      addonBg: "rgb(247, 248, 250)",
    },
    InputNumber: {
      addonBg: "rgb(247, 248, 250)",
      handleActiveBg: "rgb(247, 248, 250)",
      handleBorderColor: "rgb(222, 224, 227)",
      colorBgContainerDisabled: "rgb(240, 241, 243)",
      colorBorder: "rgb(222, 224, 227)",
    },
    Mentions: {
      activeBg: "rgb(255, 255, 255)",
      addonBg: "rgba(0, 0, 0, 0.02)",
      colorBgContainerDisabled: "rgb(240, 241, 243)",
      colorBorder: "rgb(222, 224, 227)",
      colorFillAlter: "rgb(247, 248, 250)",
      controlItemBgHover: "rgb(247, 248, 250)",
    },
    Radio: {
      buttonCheckedBgDisabled: "rgb(247, 248, 250)",
      buttonCheckedColorDisabled: "rgb(170, 170, 170)",
      buttonColor: "rgb(83, 83, 83)",
      dotColorDisabled: "rgb(240, 241, 243)",
      colorBgContainerDisabled: "rgb(240, 241, 243)",
      colorBorder: "rgb(222, 224, 227)",
    },
    Rate: {
      colorFillContent: "rgb(231, 232, 234)",
    },
    Select: {
      multipleItemBorderColorDisabled: "rgb(235, 238, 242)",
      multipleItemColorDisabled: "rgb(170, 170, 170)",
      multipleSelectorBgDisabled: "rgb(240, 241, 243)",
      optionSelectedColor: "rgb(83, 83, 83)",
      colorBgContainerDisabled: "rgb(240, 241, 243)",
      colorBorder: "rgb(222, 224, 227)",
      colorIcon: "rgb(170, 170, 170)",
      colorIconHover: "rgb(83, 83, 83)",
    },
    Slider: {
      railBg: "rgb(231, 232, 234)",
      railHoverBg: "rgb(240, 241, 243)",
      colorBgContainerDisabled: "rgb(240, 241, 243)",
      colorBorderSecondary: "rgb(235, 238, 242)",
      algorithm: true,
      colorPrimaryBorder: "rgb(22, 119, 255)",
    },
    Transfer: {
      colorBgContainerDisabled: "rgb(240, 241, 243)",
      colorBorder: "rgb(240, 241, 243)",
      colorSplit: "rgb(235, 238, 242)",
    },
    Upload: {
      colorBorder: "rgb(235, 238, 242)",
      colorBgMask: "rgba(0, 0, 0, 0.4)",
    },
    Badge: {
      colorBorderBg: "rgb(255, 255, 255)",
      colorErrorHover: "rgb(255, 120, 117)",
    },
    Calendar: {
      colorBgContainerDisabled: "rgb(240, 241, 243)",
      colorSplit: "rgb(235, 238, 242)",
      controlItemBgHover: "rgb(247, 248, 250)",
    },
    Collapse: {
      headerBg: "rgb(247, 248, 250)",
    },
    Descriptions: {
      titleColor: "rgb(46, 46, 46)",
      colorSplit: "rgb(247, 248, 250)",
      colorText: "rgb(83, 83, 83)",
      colorFillAlter: "rgb(247, 248, 250)",
    },
    Image: {
      colorBgMask: "rgb(223, 224, 225)",
      colorBgContainerDisabled: "rgb(240, 241, 243)",
    },
    List: {
      colorBorder: "rgb(240, 241, 243)",
      colorSplit: "rgb(235, 238, 242)",
    },
    Popover: {
      colorSplit: "rgb(235, 238, 242)",
    },
    Segmented: {
      itemHoverColor: "rgb(83, 83, 83)",
      itemColor: "rgb(126, 126, 126)",
      colorBgLayout: "rgb(247, 248, 250)",
      itemSelectedColor: "rgb(83, 83, 83)",
      colorFill: "rgba(22, 119, 255, 0.2)",
      colorFillSecondary: "rgba(22, 119, 255, 0.1)",
    },
    Table: {
      borderColor: "rgb(247, 248, 250)",
      headerBg: "rgb(247, 248, 250)",
      headerColor: "rgb(46, 46, 46)",
      headerFilterHoverBg: "rgb(247, 248, 250)",
      headerSortHoverBg: "rgb(251, 252, 253)",
      headerSortActiveBg: "rgb(251, 252, 253)",
      headerSplitColor: "rgb(251, 252, 253)",
      colorBorderSecondary: "rgb(235, 238, 242)",
      colorSplit: "rgb(235, 238, 242)",
    },
    Tabs: {
      controlItemBgHover: "rgb(247, 248, 250)",
      colorBorder: "rgb(222, 224, 227)",
      cardBg: "rgb(247, 248, 250)",
    },
    Tag: {
      colorBorder: "rgb(235, 238, 242)",
      borderRadiusSM: 2,
      fontSize: 12,
      lineWidth: 0,
    },
    Timeline: {
      tailColor: "rgb(235, 238, 242)",
      colorSplit: "rgb(235, 238, 242)",
    },
    Tooltip: {
      colorBgSpotlight: "rgba(0, 0, 0, 0.75)",
    },
    Tree: {
      nodeHoverBg: "rgb(247, 248, 250)",
      nodeSelectedBg: "rgba(22, 119, 255, 0.2)",
      colorBgContainerDisabled: "rgb(240, 241, 243)",
    },
    Alert: {
      colorSuccessBg: "rgb(238, 246, 232)",
      colorSuccessBorder: "rgb(207, 230, 192)",
      colorInfoBg: "rgb(229, 240, 253)",
      colorInfoBorder: "rgb(183, 214, 251)",
      colorWarningBg: "rgb(254, 243, 231)",
      colorWarningBorder: "rgb(254, 223, 189)",
      colorErrorBg: "rgb(253, 237, 237)",
      colorErrorBorder: "rgb(250, 206, 206)",
    },
    Drawer: {
      colorSplit: "rgb(235, 238, 242)",
    },
    Modal: {
      titleColor: "rgb(46, 46, 46)",
      colorBgMask: "rgba(0, 0, 0, 0.4)",
      colorSplit: "rgb(235, 238, 242)",
    },
    Progress: {
      circleTextColor: "rgb(83, 83, 83)",
      remainingColor: "rgb(231, 232, 234)",
    },
    Spin: {
      colorBgMask: "rgba(0, 0, 0, 0.4)",
    },
  },
};
export default THEME_GA;
