import { Button, Switch } from 'antd';
import { useTranslation } from 'react-i18next';
import { useWatermark } from '@/hooks/useWatermark';

function Watermark() {
  const { t } = useTranslation();
  const [Watermark, RemoveWatermark] = useWatermark();
  
  const openWatermark = () => {
    Watermark({
      content: t("content.watermark"),
      height: 300,
      width: 300,
      rotate: -20,
      color: "#cccccc",
      fontSize: 28,
      opacity: 0.2,
    });
  };

  const hidWatermark = () => {
    RemoveWatermark();
  };
  const handleSwitchWatermark = (checked: boolean) => {
    checked ? openWatermark() : hidWatermark();
  };
  return (
    <div className="p-30px">
      <Switch
        onChange={handleSwitchWatermark}
        checkedChildren={t("content.openWatermark")}
        unCheckedChildren={t("content.hideWatermark")}
        defaultChecked={false} // 设置默认开关关闭
      />
    </div>
  );
}

export default Watermark;