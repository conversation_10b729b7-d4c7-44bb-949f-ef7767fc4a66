import type { ProColumns } from '@ant-design/pro-components';
import {
  EditableProTable,
  ProCard,
  ProFormField,
  ProFormRadio,
} from '@ant-design/pro-components';
import React, { useState } from 'react';

const waitTime = (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

type DataSourceType = {
  id: React.Key;
  title?: string;
  readonly?: string;
  decs?: string;
  state?: string;
  created_at?: number;
  update_at?: number;
  children?: DataSourceType[];
};


export default (props:any) => {
  const {headers,setRequestHeaders}= props;
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<DataSourceType[]>([]);
  const [position, setPosition] = useState<'top' | 'bottom' | 'hidden'>(
    'bottom',
  );


  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '键',
      dataIndex: 'key',
      tooltip: '请求头的键，如api-key，Content-Type等',
      formItemProps: (form, { rowIndex }) => {
        return {
          rules:
            rowIndex > 1 ? [{ required: true, message: '此项为必填项' }] : [],
        };
      },
      width: "40%",
    },
    {
      title: '值',
      tooltip: '请求头的值',
      dataIndex: 'value',
      formItemProps: (form, { rowIndex }) => {
        return {
          rules:
            rowIndex > 1 ? [{ required: true, message: '此项为必填项' }] : [],
        };
      },
      width: "40%",
    },
    {
      title: '操作',
      valueType: 'option',
      width: "20%",
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        <a
          key="delete"
          onClick={() => {
            setDataSource(dataSource.filter((item) => item.id !== record.id));
          }}
        >
          删除
        </a>,
      ],
    },
  ];
  const handleDatasource = (data) => {
    const newData = data.map((item) => ({
      key: item.key,
      value: item.value,
    }));
    setDataSource(data);
    setRequestHeaders(newData); // 直接使用数组，不需要展开
  }

  return (
    <>
      <EditableProTable<DataSourceType>
        rowKey="id"
        maxLength={5}
        dataSource={headers}
        locale={{
          emptyText: '暂无数据' // 自定义空数据提示文本
        }}
        recordCreatorProps={
          position !== 'hidden'
            ? {
                position: position as 'top',
                record: () => ({ id: (Math.random() * 1000000).toFixed(0) }),
              }
            : false
        }
        loading={false}
        columns={columns}
        request={async () => ({
          data: headers,
          total: headers.length,
          success: true,
        })}
        value={dataSource}
        onChange={handleDatasource}
        editable={{
          type: 'multiple',
          editableKeys,
          onSave: async (rowKey, data, row) => {
            console.log(rowKey, data, row);
            // await waitTime(2000);
            // console.log('data',data);
            // console.log('row',row);
            console.log('dataSource',dataSource);
            setRequestHeaders(data);
          },
          onChange: setEditableRowKeys,
        }}
      />

    </>
  );
};