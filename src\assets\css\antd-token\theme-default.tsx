// AntdUI组件的token配置
const THEME_DEFAULT = {
  token: {
    colorPrimary: "#55a722",
    controlItemBgActive: "rgba(85, 167, 34, 0.10)",
    controlItemBgActiveHover: "rgba(85, 167, 34, 0.10)",
    controlOutline: "rgba(104, 185, 46, 0.20)",
    colorPrimaryBg: "rgba(85,167,34,0.10)",
    colorPrimaryBgHover: "rgba(85,167,34,0.20)",
    colorPrimaryBorder: "#55A722",
    colorPrimaryBorderHover: "#68B92E",
    colorPrimaryHover: "#68B92E",
    colorPrimaryTextHover: "#68B92E",
    colorSuccess: "#55a722",
    colorSuccessBg: "#EEF6E8",
    colorSuccessBgHover: "#EEF6E8",
    colorSuccessBorder: "#CFE6C0",
    colorSuccessBorderHover: "#CFE6C0",
    colorSuccessHover: "#68B92E",
    colorSuccessTextHover: "#68B92E",
    colorWarning: "#fa8c16",
    colorWarningBgHover: "#FEF3E7",
    colorWarningBorder: "#FEDFBD",
    colorWarningBg: "#FEF3E7",
    colorWarningBorderHover: "#FEDFBD",
    colorWarningHover: "#ffa940",
    colorError: "#ec4f4f",
    colorErrorBg: "#FDEDED",
    colorErrorBgHover: "#FDEDED",
    colorErrorBorder: "#FACECE",
    colorErrorBorderHover: "#FACECE",
    colorErrorHover: "#FF7875",
    colorErrorActive: "#D9363E",
    colorErrorTextHover: "#FF7875",
    colorErrorTextActive: "#D9363E",
    colorInfo: "#55a722",
    colorInfoBg: "#E5F0FD",
    colorInfoBgHover: "#E5F0FD",
    colorInfoBorder: "#B7D6FB",
    colorInfoBorderHover: "#B7D6FB",
    colorInfoHover: "#0A88FF",
    colorInfoActive: "#0056BB",
    colorInfoTextActive: "#0056BB",
    colorTextBase: "#535353",
    colorText: "#2E2E2E",
    colorTextSecondary: "#535353",
    colorTextTertiary: "#7E7E7E",
    colorTextQuaternary: "#AAAAAA",
    colorBorder: "#DEE0E3",
    colorBorderSecondary: "#EBEEF2",
    colorFillQuaternary: "#F7F8FA",
    colorFillTertiary: "#F0F1F3",
    colorFillSecondary: "#F7F8FA",
    colorFill: "#DFE0E1",
    colorBgLayout: "#f7f8fa",
    colorBgSpotlight: "rgba(0,0,0,0.75)",
    colorBgMask: "rgba(0, 0, 0, 0.40)",
    wireframe: true,
    borderRadius: 4,
    borderRadiusXS: 4,
    boxShadow: "0 0px 16px 0 rgba(0, 0, 0, 0.06)",
    boxShadowSecondary: "0 2px 12px 0px rgba(0, 0, 0, 0.16)    ",
    colorPrimaryActive: "#398013",
  },
  components: {
    Slider: {
      colorFillSecondary: "#E7E8EA",
      colorFillTertiary: "#D6D7D9",
      handleLineWidth: 2,
      railSize: 4,
      handleLineWidthHover: 4,
      colorFillContentHover: "#F7F8FA",
      colorBorderSecondary: "#EBEEF2",
      colorText: "#535353",
    },
    Button: {
      colorText: "#535353",
      paddingContentHorizontal: 16,
      colorErrorBg: "#FDEDED",
      colorBgTextActive: "#DFE0E1",
      colorErrorOutline: "rgba(230, 30, 30, 0.08)",
      controlTmpOutline: "rgba(104,185,46,0.1)",
      controlOutline: "#ffffff",
      controlOutlineWidth: 0,
      colorLink: "#0072EE",
      textHoverBg: "rgb(247, 248, 250)",
    },
    Divider: {
      colorSplit: "#EBEEF2",
      colorText: "#535353",
    },
    Dropdown: {
      colorText: "#535353",
      controlItemBgHover: "#F7F8FA",
      colorSplit: "rgb(235, 238, 242)",
    },
    Breadcrumb: {
      colorText: "#535353",
    },
    Menu: {
      itemSelectedBg: "#55a722",
      itemSelectedColor: "#ffffff",
      radiusItem: 0,
      itemBorderRadius: 0,
      radiusSubMenuItem: 0,
      subMenuItemBorderRadius: 0,
      darkItemBg: "#26303a",
      darkSubMenuItemBg: "#26303a",
      colorBgContainer: "#26303a",
      colorBgElevated: "#26303a",
      itemHoverColor: "#ffffff",
      itemHoverBg: "#26303a",
      itemColor: "rgba(255,255,255,0.65)",
      marginXXS: "0px",
    },
    Pagination: {
      colorBgContainerDisabled: "#F0F1F3",
      colorText: "#535353",
      itemActiveBgDisabled: "rgb(240, 241, 243)",
      itemActiveColorDisabled: "rgb(170, 170, 170)",
    },
    Steps: {
      colorBorderSecondary: "#EBEEF2",
      colorSplit: "rgb(235, 238, 242)",
    },
    Cascader: {
      colorBgContainerDisabled: "#F0F1F3",
      colorHighlight: "#F1A45D",
      colorSplit: "#EBEEF2",
      colorText: "#535353",
      controlItemBgHover: "#f7f8fa",
      controlItemBgActive: "rgba(85, 167, 34, 0.10)",
    },
    Checkbox: {
      borderRadiusSM: 2,
      colorBgContainerDisabled: "#F0F1F3",
      colorText: "#535353",
    },
    DatePicker: {
      borderRadius: 4,
      borderRadiusLG: 4,
      boxShadowPopoverArrow: "0px 2px 12px 0px rgba(0,0,0,0.16)",
      boxShadowSecondary: " 0px 2px 12px 0px rgba(0,0,0,0.16)",
      colorIcon: "#AAAAAA",
      colorIconHover: "#535353",
      colorLink: "#55A722",
      colorLinkHover: "#68B92E",
      colorLinkActive: "#398013",
      colorSplit: "#EBEEF2",
      colorText: "#535353",
      controlItemBgHover: "#F7F8FA",
    },
    Form: {
      colorTextDescription: "#AAAAAA",
    },
    Input: {
      colorIcon: "#AAAAAA",
      colorIconHover: "#535353",
      colorText: "#535353",
    },
    InputNumber: {
      colorText: "#535353",
    },
    Mentions: {
      colorText: "#535353",
      controlItemBgHover: "#F7F8FA",
    },
    Radio: {
      colorText: "#535353",
      controlItemBgActiveDisabled: "#F0F1F3",
    },
    Rate: {
      colorText: "#535353",
      yellow6: "#fadb14",
      colorFillContent: "rgb(231, 232, 234)",
    },
    Select: {
      boxShadowSecondary: "0px 2px 12px 0px rgba(0,0,0,0.16)",
      colorIconHover: "#535353",
      colorIcon: "#AAAAAA",
      colorSplit: "#EBEEF2",
      colorText: "#535353",
      controlItemBgHover: "#f7f8fa",
      controlItemBgActive: "rgba(85, 167, 34, 0.10)",
    },
    Switch: {
      colorTextQuaternary: "#B4B6BB",
      colorTextTertiary: "#7E7E7E",
    },
    Transfer: {
      colorSplit: "#EBEEF2",
      colorText: "#535353",
      controlItemBgHover: "#F7F8FA",
      controlItemBgActive: "rgba(85, 167, 34, 0.10)",
    },
    TreeSelect: {
      colorText: "#535353",
      controlItemBgActive: "rgba(85, 167, 34, 0.10)",
      controlItemBgHover: "#F7F8FA",
    },
    Upload: {
      colorText: "#535353",
      controlItemBgHover: "#f7f8fa",
    },
    Avatar: {
      colorText: "#535353",
    },
    Badge: {
      blue1: "#E7F0FC",
      blue3: "#CFDFF8",
      blue6: "#5F96EA",
      blue7: "#3D7EDF",
      colorText: "#535353",
      cyan1: "#D9EDED",
      cyan6: "#008685",
      cyan7: "#006C6C",
      cyan3: "#B2DADA",
      geekblue1: "#E8F2FD",
      geekblue3: "#CFE4FB",
      geekblue6: "#62A7F2",
      geekblue7: "#3A8FED",
      gold7: "#DBA838",
      gold6: "#F2C664",
      gold3: "#FBEDD0",
      gold1: "#FDF7E8",
      green1: "#E6F2DE",
      green3: "#CCE4BC",
      green6: "#55A722",
      green7: "#398013",
      magenta1: "#F5ECFC",
      magenta3: "#EAD8F7",
      magenta6: "#BA7DE7",
      magenta7: "#A158D5",
      orange1: "#FDF2E7",
      orange3: "#FAE3CE",
      orange6: "#F1A45D",
      orange7: "#D68438",
      pink1: "#FFAFAF",
      pink3: "#FF8585",
      pink6: "#eb2f96",
      pink7: "#c41d7f",
      purple1: "#E9E9FD",
      purple3: "#D2D2FB",
      purple6: "#6B69F2",
      purple7: "#4A47EA",
      volcano1: "#E8EBF0",
      volcano3: "#D0D6E1",
      volcano6: "#65789B",
      volcano7: "#3E5784",
      red1: "#FAE6E6",
      red3: "#F5CBCB",
      red7: "#C62E2E",
      red6: "#DE5454",
    },
    Calendar: {
      colorIcon: "#AAAAAA",
      colorIconHover: "#535353",
      colorLink: "#55A722",
      colorLinkActive: "#398013",
      colorLinkHover: "#68B92E",
      colorSplit: "#EBEEF2",
      colorText: "#535353",
      controlItemBgHover: "#F7F8FA",
      controlItemBgActive: "rgba(85, 167, 34, 0.10)",
    },
    Card: {
      boxShadowCard: "      0px 0px 16px 0px rgba(0,0,0,0.06)",
      boxShadowTertiary: "",
      colorBorderSecondary: "#EBEEF2",
      fontWeightStrong: 500,
    },
    List: {
      colorBorder: "#EBEEF2",
      colorSplit: "#EBEEF2",
    },
    Table: {
      boxShadowSecondary: "      0px 0px 16px 0px rgba(0,0,0,0.06)",
      colorLink: "#55A722",
      colorLinkActive: "#398013",
      colorLinkHover: "#68B92E",
      controlItemBgActive: "rgba(85, 167, 34, 0.10)",
      controlItemBgHover: "#F7F8FA",
      fontWeightStrong: 500,
      colorIcon: "#AAAAAA",
      colorIconHover: "#535353",
      colorFillSecondary: "#F0F1F3",
      colorFillContent: "#F0F1F3",
      controlItemBgActiveHover: "rgba(85, 167, 34, 0.10)",
    },
    Collapse: {
      colorText: "#535353",
      colorBorder: "#EBEEF2",
    },
    Descriptions: {
      colorSplit: "#EBEEF2",
      colorText: "#535353",
      colorTextSecondary: "#2E2E2E",
    },
    Tabs: {
      boxShadowSecondary: "0 2px 12px 0px rgba(0, 0, 0, 0.16)    ",
      colorBorder: "#EBEEF2",
      colorText: "#535353",
      controlItemBgHover: "#F7F8FA",
    },
    Segmented: {
      boxShadowTertiary: "      0px 0px 16px 0px rgba(0,0,0,0.06)",
      colorFill: "rgba(85, 167, 34, 0.20)",
      colorBgElevated: "#ffffff",
      colorFillSecondary: "rgba(85, 167, 34, 0.10)",
    },
    Popover: {
      colorSplit: "#EBEEF2",
      boxShadowPopoverArrow: "0px 2px 12px 0px rgba(0,0,0,0.16)",
      blue6: "#5F96EA",
      colorText: "#535353",
      fontWeightStrong: 400,
      cyan6: "#008685",
      geekblue6: "#62A7F2",
      gold6: "#F2C664",
      green6: "#55A722",
      magenta6: "#BA7DE7",
      orange6: "#F1A45D",
      purple6: "#6B69F2",
      red6: "#DE5454",
      volcano6: "#65789B",
    },
    Statistic: {
      colorText: "#535353",
    },
    Tag: {
      borderRadiusSM: 2,
      blue1: "#E7F0FC",
      blue6: "#5F96EA",
      blue7: "#3D7EDF",
      blue3: "#CFDFF8",
      colorBorder: "#EBEEF2",
      colorFillSecondary: "#F7F8FA",
      colorFillQuaternary: "#F7F8FA",
      colorText: "#535353",
      cyan1: "#D9EDED",
      cyan3: "#B2DADA",
      cyan6: "#008685",
      cyan7: "#006C6C",
      geekblue1: "#E8F2FD",
      geekblue3: "#CFE4FB",
      geekblue6: "#62A7F2",
      geekblue7: "#3A8FED",
      lineWidth: 0,
      fontSize: 12,
      gold1: "#FDF7E8",
      gold3: "#FBEDD0",
      gold6: "#F2C664",
      gold7: "#DBA838",
      green1: "#E6F2DE",
      green3: "#CCE4BC",
      green6: "#55A722",
      green7: "#398013",
      magenta1: "#F5ECFC",
      magenta3: "#EAD8F7",
      magenta6: "#BA7DE7",
      magenta7: "#A158D5",
      orange1: "#FDF2E7",
      orange3: "#FAE3CE",
      orange6: "#F1A45D",
      orange7: "#D68438",
      purple1: "#E9E9FD",
      purple3: "#D2D2FB",
      purple6: "#6B69F2",
      purple7: "#4A47EA",
      red1: "#FAE6E6",
      red3: "#F5CBCB",
      red6: "#DE5454",
      red7: "#C62E2E",
      volcano1: "#E8EBF0",
      volcano3: "#D0D6E1",
      volcano6: "#65789B",
      volcano7: "#3E5784",
    },
    Timeline: {
      colorSplit: "#DEE0E3",
      colorText: "#535353",
    },
    Tooltip: {
      colorBgDefault: "rgba(0, 0, 0, 0.75)",
      blue1: "#E7F0FC",
      blue3: "#CFDFF8",
      blue6: "#5F96EA",
      blue7: "#3D7EDF",
      cyan1: "#D9EDED",
      cyan3: "#B2DADA",
      cyan6: "#008685",
      cyan7: "#006C6C",
      geekblue1: "#E8F2FD",
      geekblue6: "#62A7F2",
      geekblue7: "#3A8FED",
      geekblue3: "#CFE4FB",
      gold1: "#FDF7E8",
      gold3: "#FBEDD0",
      gold6: "#F2C664",
      gold7: "#DBA838",
      green1: "#E6F2DE",
      green6: "#55A722",
      green7: "#398013",
      green3: "#CCE4BC",
      magenta1: "#F5ECFC",
      magenta3: "#EAD8F7",
      magenta6: "#BA7DE7",
      magenta7: "#A158D5",
      orange1: "#FDF2E7",
      orange3: "#FAE3CE",
      orange6: "#F1A45D",
      orange7: "#D68438",
      purple1: "#E9E9FD",
      purple3: "#D2D2FB",
      purple6: "#6B69F2",
      purple7: "#4A47EA",
      red1: "#FAE6E6",
      red3: "#F5CBCB",
      red6: "#DE5454",
      red7: "#C62E2E",
      volcano1: "#E8EBF0",
      volcano3: "#D0D6E1",
      volcano6: "#65789B",
      volcano7: "#3E5784",
    },
    Tree: {
      borderRadius: 2,
      borderRadiusSM: 2,
      colorText: "#535353",
      controlItemBgActive: "rgba(85, 167, 34, 0.10)",
      controlItemBgHover: "#F7F8FA",
    },
    Alert: {
      colorIcon: "#AAAAAA",
      colorIconHover: "#535353",
      colorText: "#535353",
    },
    Drawer: {
      colorIcon: "#AAAAAA",
      colorIconHover: "#535353",
      lineWidth: 1,
      colorSplit: "#EBEEF2",
    },
    Message: {
      boxShadow: "0 2px 12px 0 rgba(0, 0, 0, 0.16)",
      colorText: "#535353",
    },
    Modal: {
      colorBgElevated: "#ffffff",
      colorSplit: "#EBEEF2",
      colorIcon: "#AAAAAA",
      colorIconHover: "#535353",
      colorText: "#535353",
      boxShadow: "0 2px 12px 0 rgba(0, 0, 0, 0.16)",
    },
    Notification: {
      boxShadow: "0 2px 12px 0 rgba(0, 0, 0, 0.16)",
      colorIcon: "#AAAAAA",
      colorIconHover: "#535353",
      colorText: "#535353",
    },
    Popconfirm: {
      colorText: "#535353",
    },
    Progress: {
      colorFillSecondary: "#E7E8EA",
      colorText: "#535353",
    },
    Result: {
      subtitleFontSize: 16,
    },
    Anchor: {
      colorSplit: "#EBEEF2",
      colorText: "#535353",
      lineType: "solid",
    },
  },
};
export default THEME_DEFAULT;
