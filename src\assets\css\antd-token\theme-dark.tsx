// AntdUI组件的token配置
// const THEME_DARK = {

//   colorPrimary: "#438b16",
//   colorPrimaryBg: "#1C1C1E",
//   colorPrimaryBgHover: "#3A3A3C",
//   colorPrimaryBorder: "#438b16",
//   colorPrimaryBorderHover: "#438b16",
//   colorPrimaryHover: "#5C9931",
//   colorPrimaryActive: "#2B660B",
//   colorPrimaryTextHover: "#5C9931",
//   colorPrimaryText: "#438b16",
//   colorPrimaryTextActive: "#2B660B",
//   colorSuccess: "#438b16",
//   colorSuccessBg: "#2D312A",
//   colorSuccessBgHover: "#2D312A",
//   colorSuccessBorder: "#50852F",
//   colorSuccessBorderHover: "#50852F",
//   colorSuccessHover: "#5C9931",
//   colorSuccessActive: "#2B660B",
//   colorSuccessTextHover: "#5C9931",
//   colorSuccessText: "#438B16",
//   colorSuccessTextActive: "#2B660B",
//   colorWarning: "#db7e1a",
//   colorWarningBg: "#332C25",
//   colorWarningBgHover: "#332C25",
//   colorWarningBorder: "#8B6F51",
//   colorWarningBorderHover: "#8B6F51",
//   colorErrorBg: "#332C2C",
//   colorErrorBgHover: "#332C2C",
//   colorErrorBorder: "#A05A55",
//   colorErrorBorderHover: "#e6847a",
//   colorTextBase: "#afafb0",
//   colorBgBase: "#1c1c1e",
//   colorText: "#AFAFB0",
//   colorTextSecondary: "#AFAFB0",
//   colorTextTertiary: "#989899",
//   colorTextQuaternary: "#6B6B6C",
//   colorBorder: "#48484A",
//   colorBorderSecondary: "#444447",
//   colorBgContainer: "#1C1C1E",
//   colorBgElevated: "#3a3a3c",
//   colorBgLayout: "#000000",
//   colorBgSpotlight: "rgba(0, 0, 0, 0.9)",
//   colorBgMask: "rgba(0, 0, 0, 0.9)",
//   colorInfoBorder: "#4E719C",
//   colorInfoBorderHover: "#4E719C",
//   colorInfo: "#438b16",
//   colorInfoBgHover: "#2A2D30",
//   colorInfoBg: "#2A2D30",
//   colorErrorHover: "#e6847a",
//   colorError: "#d95a50",
//   colorErrorActive: "#b33d39",
//   colorErrorTextActive: "#b33d39",
//   colorErrorText: "#d95a50",
//   colorErrorTextHover: "#e6847a",
//   Segmented: {
//     itemActiveBg: "transparent",
//     itemSelectedBg: "#438b16",
//     itemColor: "#AFAFB0",
//     itemHoverColor: "#DDDDDD",
//     itemHoverBg: "(0,0,0,0.3)",
//     itemSelectedColor: "rgb(221, 221, 221)",
//     boxShadowTertiary:
//       "      0 2px 16px 0 rgba(0, 0, 0, 0.7),      0 1px 6px -1px rgba(0, 0, 0, 0.02),      0 2px 4px 0 rgba(0, 0, 0, 0.02)    ",
//     colorTextLabel: "rgb(221, 221, 221)",
//     colorText: "rgb(221, 221, 221)",
//     colorBgLayout: "rgb(28, 28, 30)",
//   },
//   Button: {
//     colorErrorActive: "#b33d39",
//     colorErrorBorderHover: "#e6847a",
//     colorErrorHover: "#e6847a",
//     colorBgTextActive: "#2B660B",
//     colorBgContainerDisabled: "rgb(39, 39, 41)",
//     colorBgTextHover: "#5C9931",
//     colorErrorBg: "#332C2C",
//     colorErrorOutline: "#e6847a",
//     colorLink: "#1973D4",
//     colorLinkActive: "#0c55ad",
//     colorLinkHover: "#3f92e0",
//     colorPrimary: "#438B16",
//     colorPrimaryBorder: "#438B16",
//     colorText: "#AFAFB0",
//     colorTextDisabled: "#545456",
//     colorTextLightSolid: "#DDDDDD",
//     controlOutline: "#76A650",
//     controlOutlineWidth: 1,
//     controlTmpOutline: "transparent",
//     colorBgContainer: "transparent",
//     colorPrimaryHover: "#5C9931",
//     colorBorder: "#48484A",
//     borderColorDisabled: "rgb(72, 72, 74)",
//   },
//   Select: {
//     controlItemBgHover: "rgb(40, 41, 43)",
//     controlItemBgActive: "rgb(58, 58, 60)",
//     controlOutline: "#48484A",
//     colorBgElevated: "rgb(28, 28, 30)",
//     colorBgContainer: "#1C1C1E",
//     colorBgContainerDisabled: "rgb(39, 39, 41)",
//     multipleItemBg: "rgb(43, 43, 47)",
//     multipleItemBorderColor: "rgba(249, 0, 0, 0)",
//     optionActiveBg: "rgb(43, 43, 47)",
//     optionSelectedBg: "rgb(56, 56, 59)",
//     colorTextDescription: "rgb(107, 107, 108)",
//     colorTextDisabled: "rgb(84, 84, 86)",
//   },
//   Slider: {
//     railBg: "#3A3a3c",
//     railHoverBg: "#45464a",
//     dotBorderColor: "#DDDDDD",
//     colorBgContainer: "rgb(28, 28, 30)",
//     colorTextDisabled: "rgb(84, 84, 86)",
//     colorTextDescription: "rgb(107, 107, 108)",
//   },
//   Badge: {
//     colorBorderBg: "transparent",
//     colorBgContainer: "#fff",
//   },
//   Collapse: {
//     colorTextDisabled: "#545456",
//     colorText: "#AFAFB0",
//     colorFillAlter: "rgb(43, 43, 47)",
//   },
//   Descriptions: {
//     extraColor: "#2B2B2F",
//     labelBg: "#2B2B2F",
//   },
//   Table: {
//     colorTextDisabled: "#272729",
//     colorTextPlaceholder: "#6B",
//     controlItemBgActiveHover: "#2f531a",
//     controlItemBgHover: "rgb(40, 41, 43)",
//     boxShadowSecondary: "(0,0,100,0)",
//     colorTextDescription: "#AFAFB0",
//     colorFillAlter: "#2B2B2F",
//     colorFillSecondary: "#2B2B2F",
//     colorFillContent: "#28292B",
//     colorLink: "#1973D4",
//     colorLinkHover: "#3f92e0",
//     colorLinkActive: "#1973D4",
//   },
//   Modal: {
//     footerBg: "transparent",
//     contentBg: "#1C1C1E",
//     boxShadow: "0 2px 16px 0 rgba(0, 0, 0, 0.7)",
//     colorBgElevated: "#1C1C1E",
//     colorError: "#d95a50",
//     colorWarning: "#db7e1a",
//     colorSuccess: "#438b16",
//     colorSplit: "#38383A",
//     colorText: "#AFAFB0",
//     colorTextHeading: "#dddddd",
//   },
//   Rate: {
//     starBg: "#2B2B2F",
//   },
//   Calendar: {
//     controlItemBgHover: "#28292B",
//     colorTextDisabled: "#545456",
//     colorText: "#AFAFB0",
//     colorSplit: "#38383A",
//     colorPrimaryHover: "#5C9931",
//     colorLinkHover: "#3f92e0",
//     colorLinkActive: "#0c55ad",
//     fullBg: "rgb(28, 28, 30)",
//     colorBgContainerDisabled: "rgb(39, 39, 41)",
//   },
//   Notification: {
//     colorText: "#AFAFB0",
//     boxShadow:
//       "      0 6px 16px 0 rgba(0, 0, 0, 0.2),      0 3px 6px -4px rgba(0, 0, 0, 0.2),      0 9px 28px 8px rgba(0, 0, 0, 0.15)    ",
//     colorInfo: "#1973D4",
//     colorSuccess: "#438B16",
//     colorError: "#D95A50",
//     colorTextHeading: "#dddddd",
//     colorWarning: "#DB7E1A",
//     colorBgBlur: "rgba(0, 0, 0, 0.16)",
//     colorBgElevated: "rgb(28, 28, 30)",
//   },
//   Popconfirm: {
//     colorText: "#AFAFB0",
//   },
//   Skeleton: {
//     color: "#3A3A3C",
//   },
//   Card: {
//     colorBorderSecondary: "#48484A",
//     colorText: "#AFAFB0",
//     headerBg: "transparent",
//     actionsBg: "#ffffff",
//   },
//   Tooltip: {
//     colorBgDefault: "#3A3A3C",
//     colorTextLightSolid: "rgb(221, 221, 221)",
//   },
//   Message: {
//     contentBg: "rgb(28, 28, 30)",
//     boxShadow: "      0 2px 16px 0 rgba(0, 0, 0, 0.7)",
//   },
//   Menu: {
//     darkItemSelectedBg: "#438b16",
//     radiusItem: 0,
//     itemBorderRadius: 0,
//     radiusSubMenuItem: 0,
//     subMenuItemBorderRadius: 0,
//     darkItemBg: "#1C1C1E",
//     darkSubMenuItemBg: "#1c1c1",
//     colorBgContainer: "#1C1C1E",
//     colorBgElevated: "#1C1C1E",
//     darkItemHoverColor: "#5c9931",
//     darkItemColor: "rgba(255,255,255,0.85)",
//     marginXXS: "0px",
//   },
//   Dropdown: {
//     controlItemBgActiveHover: "rgb(43, 43, 47)",
//     controlItemBgHover: "rgb(43, 43, 47)",
//     controlItemBgActive: "rgb(56, 56, 59)",
//     colorBgElevated: "rgb(28, 28, 30)",
//     colorError: "#D95A50",
//     colorPrimary: "rgb(221, 221, 221)",
//     colorSplit: "rgb(68, 68, 71)",
//     colorTextDisabled: "#545456",
//     boxShadowSecondary: "      0 2px 16px 0 rgba(0, 0, 0, 0.7)",
//     colorTextDescription: "rgb(175, 175, 176)",
//     colorText: "rgb(221, 221, 221)",
//     colorPrimaryBorder: "rgb(72, 72, 74)",
//   },
//   Steps: {
//     colorBorderBg: "transparent",
//     colorBorderSecondary: "transparent",
//   },
//   Breadcrumb: {
//     itemColor: "rgb(175, 175, 176)",
//     lastItemColor: "#DDDDDD",
//     linkColor: "rgb(175, 175, 176)",
//     linkHoverColor: "#DDDDDD",
//     separatorColor: "rgb(175, 175, 176)",
//     colorTextDescription: "rgb(175, 175, 176)",
//   },
//   Cascader: {
//     controlItemBgHover: "rgb(43, 43, 47)",
//     controlItemBgActive: "rgb(56, 56, 59)",
//     colorBgContainerDisabled: "rgb(43, 43, 47)",
//     colorSplit: "rgb(68, 68, 71)",
//     colorText: "rgb(175, 175, 176)",
//     colorTextDescription: "rgb(107, 107, 108)",
//     colorTextDisabled: "rgb(84, 84, 86)",
//   },
//   Mentions: {
//     controlItemBgHover: "rgb(43, 43, 47)",
//     colorBgElevated: "rgb(28, 28, 30)",
//     colorBgContainerDisabled: "#2B2B2F",
//     controlOutline: "#76A650",
//     activeBg: "rgba(255, 255, 255, 0)",
//     hoverBg: "rgba(255, 255, 255, 0.01)",
//     activeBorderColor: "rgb(67, 139, 22)",
//   },
//   DatePicker: {
//     colorBgContainer: "transparent",
//     colorBgElevated: "rgb(28, 28, 30)",
//     controlItemBgHover: "#3A3A3C",
//     controlItemBgActive: "#4A4A4D",
//     colorErrorOutline: "#e6847a",
//     colorIconHover: "#dddddd",
//     colorBgContainerDisabled: "#2B2B2F",
//     controlOutline: "#76A650",
//     controlOutlineWidth: 1,
//     colorWarningOutline: "#e89d41",
//     activeShadow: "0 2 16 0px rgba(0, 0, 0, 0.7)",
//     warningActiveShadow: "0 2 16 0px rgba(0, 0, 0, 0.7)",
//     errorActiveShadow: "0 2 16 0px rgba(0, 0, 0, 0.7)",
//     cellBgDisabled: "rgb(43, 43, 47)",
//   },
//   Pagination: {
//     controlOutline: "#76A650",
//     controlOutlineWidth: 1,
//     controlItemBgActiveDisabled: "#2B2B2F",
//     colorBgTextHover: "rgb(43, 43, 47)",
//     colorBgContainerDisabled: "rgb(43, 43, 47)",
//   },
//   TreeSelect: {
//     colorBgContainerDisabled: "#2B2B2F",
//     colorBgElevated: "rgb(28, 28, 30)",
//     colorTextDisabled: "#545456",
//     controlItemBgActive: "rgb(58, 58, 60)",
//     controlItemBgHover: "rgb(40, 41, 43)",
//     colorTextLightSolid: "#DDD",
//     colorBorder: "transparent",
//   },
//   Form: {
//     controlOutline: "#76A650",
//     controlOutlineWidth: 1,
//     colorPrimary: "#438B16",
//     colorSuccess: "#438B16",
//     colorError: "#D95A50",
//     colorWarning: "#DB7E1A",
//     colorTextDescription: "#6B6B6C",
//   },
//   Tabs: {
//     cardBg: "rgba(0, 0, 0, 0.02)",
//     colorBgContainer: "transparent",
//     colorPrimary: "#438B16",
//     colorTextDisabled: "#545456",
//     inkBarColor: "#438B16",
//   },
//   Progress: {
//     colorError: "#D95A50",
//     colorInfo: "#1973D4",
//     colorSuccess: "#438B16",
//     colorFillSecondary: "#3A3A3C",
//   },
//   Result: {
//     colorError: "#D95A50",
//     colorInfo: "#1973D4",
//     colorSuccess: "#438B16",
//     colorWarning: "#DB7E1A",
//   },
//   Spin: {
//     colorPrimary: "#438B16",
//   },
//   Anchor: {
//     colorPrimary: "#438B16",
//     colorSplit: "rgb(68, 68, 71)",
//   },
//   Typography: {
//     colorError: "#D95A50",
//     colorLink: "#1973D4",
//     colorLinkHover: "#3f92e0",
//     colorLinkActive: "#0c55ad",
//     colorErrorHover: "#e6847a",
//     colorSuccess: "#438B16",
//     colorWarning: "#DB7E1A",
//   },
//   Tree: {
//     directoryNodeSelectedBg: "rgb(67, 139, 22)",
//   },
//   Popover: {
//     colorBgElevated: "rgb(28, 28, 30)",
//     boxShadowSecondary: "0 2px 16px 0 rgba(0, 0, 0, 0.6)",
//   },
//   Divider: {
//     colorSplit: "rgb(68, 68, 71)",
//   },
//   Statistic: {
//     colorTextHeading: "rgb(221, 221, 221)",
//   },
//   Checkbox: {
//     colorBgContainerDisabled: "rgb(43, 43, 47)",
//     colorBorder: "rgb(68, 68, 71)",
//   },
//   Radio: {
//     colorPrimaryActive: "rgba(43, 102, 11, 0)",
//   },
//   Switch: {
//     handleBg: "rgb(221, 221, 221)",
//   },
// };

const THEME_DARK = {
  token: {
    colorPrimaryBg: "#1C1C1E",
    colorPrimaryBgHover: "#1c1c1e",
    colorPrimaryBorder: "#438b16",
    colorPrimaryBorderHover: "#438b16",
    colorPrimaryHover: "#5C9931",
    colorPrimaryActive: "#2B660B",
    colorPrimaryTextHover: "#5C9931",
    colorPrimaryText: "#438b16",
    colorPrimaryTextActive: "#2B660B",
    colorSuccess: "#438b16",
    colorSuccessBg: "#2D312A",
    colorSuccessBgHover: "#2D312A",
    colorSuccessBorder: "#50852F",
    colorSuccessBorderHover: "#50852F",
    colorSuccessHover: "#5C9931",
    colorSuccessActive: "#2B660B",
    colorSuccessTextHover: "#5C9931",
    colorSuccessText: "#438B16",
    colorSuccessTextActive: "#2B660B",
    colorWarning: "#db7e1a",
    colorWarningBg: "#332C25",
    colorWarningBgHover: "#332C25",
    colorWarningBorder: "#8B6F51",
    colorWarningBorderHover: "#8B6F51",
    colorErrorBg: "#332C2C",
    colorErrorBgHover: "#332C2C",
    colorErrorBorder: "#A05A55",
    colorErrorBorderHover: "#e6847a",
    colorTextBase: "#afafb0",
    colorBgBase: "#1c1c1e",
    colorText: "#dddddd",
    colorTextSecondary: "#AFAFB0",
    colorTextTertiary: "#6b6b6c",
    colorTextQuaternary: "#545456",
    colorBorder: "#48484A",
    colorBorderSecondary: "#444447",
    colorBgContainer: "#1C1C1E",
    colorBgElevated: "#242426",
    colorBgLayout: "#000000",
    colorBgMask: "rgba(0, 0, 0, 0.9)",
    colorInfoBorder: "#4E719C",
    colorInfoBorderHover: "#4E719C",
    colorInfoBgHover: "#2A2D30",
    colorInfoBg: "#2A2D30",
    colorErrorHover: "#e6847a",
    colorError: "#d95a50",
    colorErrorActive: "#b33d39",
    colorErrorTextActive: "#b33d39",
    colorErrorText: "#d95a50",
    colorErrorTextHover: "#e6847a",
    colorBgSpotlight: "#ffffff1a",
    wireframe: false,
    borderRadius: 4,
    borderRadiusXS: 4,
    colorPrimary: "#438b16",
    colorInfo: "#438b16",
    boxShadow:
      "0 0px 16px 16 rgba(0, 0, 0, 0.6)\n0 0px 16px 16 rgba(0, 0, 0, 0.6)\n",
    boxShadowSecondary: "0px 0px 16px rgba(0, 0, 0, 0.16)",
  },
  components: {
    Segmented: {
      itemActiveBg: "rgb(28, 28, 30)",
      itemSelectedBg: "#438b16",
      itemHoverBg: "rgba(255, 255, 255, 0.2)",
      itemSelectedColor: "rgb(255, 255, 255)",
      boxShadowTertiary:
        "      0 2px 16px 0 rgba(0, 0, 0, 0.7),      0 1px 6px -1px rgba(0, 0, 0, 0.02),      0 2px 4px 0 rgba(0, 0, 0, 0.02)    ",
      colorTextLabel: "rgb(221, 221, 221)",
      colorText: "rgb(221, 221, 221)",
      colorBgLayout: "rgb(0, 0, 0)",
      itemHoverColor: "rgb(221, 221, 221)",
      itemColor: "rgb(221, 221, 221)",
    },
    Button: {
      colorErrorActive: "#b33d39",
      colorErrorBorderHover: "#e6847a",
      colorErrorHover: "#e6847a",
      colorBgTextActive: "#2B660B",
      colorBgContainerDisabled: "rgb(39, 39, 41)",
      colorBgTextHover: "#5C9931",
      colorErrorBg: "#332C2C",
      colorErrorOutline: "rgba(230, 132, 122, 0)",
      colorLink: "#1973D4",
      colorLinkActive: "#0c55ad",
      colorLinkHover: "#3f92e0",
      colorPrimary: "#438B16",
      colorPrimaryBorder: "#438B16",
      colorText: "rgb(221, 221, 221)",
      colorTextDisabled: "#545456",
      colorTextLightSolid: "#DDDDDD",
      controlOutline: "rgba(0, 0, 0, 0)",
      controlOutlineWidth: 1,
      controlTmpOutline: "transparent",
      colorBgContainer: "transparent",
      colorPrimaryHover: "#5C9931",
      colorBorder: "#48484A",
      borderColorDisabled: "rgb(72, 72, 74)",
      defaultBorderColor: "rgb(72, 72, 74)",
      defaultColor: "rgb(175, 175, 176)",
    },
    Select: {
      controlItemBgHover: "rgb(40, 41, 43)",
      controlItemBgActive: "rgb(58, 58, 60)",
      controlOutline: "#48484A",
      colorBgElevated: "rgb(28, 28, 30)",
      colorBgContainer: "#1C1C1E",
      colorBgContainerDisabled: "rgb(39, 39, 41)",
      multipleItemBg: "rgb(43, 43, 47)",
      multipleItemBorderColor: "rgba(249, 0, 0, 0)",
      optionSelectedBg: "rgba(67, 139, 22, 0.3)",
      colorTextDescription: "rgb(107, 107, 108)",
      colorTextDisabled: "rgb(84, 84, 86)",
    },
    Slider: {
      railBg: "rgb(56, 56, 59)",
      dotBorderColor: "#DDDDDD",
      colorBgContainer: "rgb(28, 28, 30)",
      colorTextDisabled: "rgb(84, 84, 86)",
      colorTextDescription: "rgb(107, 107, 108)",
      colorPrimaryBorderHover: "rgb(92, 153, 49)",
      railHoverBg: "rgb(84, 84, 86)",
    },
    Badge: {
      colorBorderBg: "rgb(255, 255, 255)",
      colorBgContainer: "#fff",
    },
    Collapse: {
      colorTextDisabled: "#545456",
      colorText: "#AFAFB0",
      colorFillAlter: "rgb(43, 43, 47)",
    },
    Descriptions: {
      extraColor: "#2B2B2F",
      labelBg: "#2B2B2F",
      colorFillAlter: "rgb(43, 43, 47)",
      colorSplit: "rgb(43, 43, 47)",
      titleColor: "rgb(221, 221, 221)",
    },
    Table: {
      colorTextDisabled: "#272729",
      colorTextPlaceholder: "#6B",
      controlItemBgActiveHover: "#2f531a",
      controlItemBgHover: "rgb(40, 41, 43)",
      boxShadowSecondary: "(0,0,100,0)",
      colorTextDescription: "#AFAFB0",
      colorFillAlter: "#2B2B2F",
      colorFillSecondary: "#2B2B2F",
      colorFillContent: "#28292B",
      colorLink: "#1973D4",
      colorLinkHover: "#3f92e0",
      colorLinkActive: "#1973D4",
      headerBg: "rgb(43, 43, 47)",
      headerColor: "rgb(221, 221, 221)",
      rowHoverBg: "rgb(43, 43, 47)",
      headerSplitColor: "rgb(68, 68, 71)",
      headerFilterHoverBg: "rgb(43, 43, 47)",
      headerSortHoverBg: "rgb(43, 43, 47)",
      stickyScrollBarBg: "rgb(121, 121, 125)",
    },
    Modal: {
      footerBg: "transparent",
      contentBg: "rgb(36, 36, 38)",
      boxShadow: "0 2px 16px 0 rgba(0, 0, 0, 0.7)",
      colorBgElevated: "#1C1C1E",
      colorError: "#d95a50",
      colorWarning: "#db7e1a",
      colorSuccess: "#438b16",
      colorSplit: "#38383A",
      colorText: "#AFAFB0",
      colorTextHeading: "#dddddd",
    },
    Rate: {
      starBg: "#2B2B2F",
      colorFillContent: "rgb(56, 56, 59)",
    },
    Calendar: {
      controlItemBgHover: "#28292B",
      colorTextDisabled: "#545456",
      colorText: "#AFAFB0",
      colorSplit: "#38383A",
      colorPrimaryHover: "#5C9931",
      colorLinkHover: "#3f92e0",
      colorLinkActive: "#0c55ad",
      fullBg: "rgb(28, 28, 30)",
      colorBgContainerDisabled: "rgb(39, 39, 41)",
      controlItemBgActive: "rgba(67, 139, 22, 0.2)",
    },
    Notification: {
      colorText: "#AFAFB0",
      boxShadow:
        "      0 6px 16px 0 rgba(0, 0, 0, 0.2),      0 3px 6px -4px rgba(0, 0, 0, 0.2),      0 9px 28px 8px rgba(0, 0, 0, 0.15)    ",
      colorInfo: "#1973D4",
      colorSuccess: "#438B16",
      colorError: "#D95A50",
      colorTextHeading: "#dddddd",
      colorWarning: "#DB7E1A",
      colorBgBlur: "rgba(0, 0, 0, 0.16)",
      colorBgElevated: "rgb(36, 36, 38)",
    },
    Skeleton: {
      color: "#3A3A3C",
    },
    Card: {
      headerBg: "transparent",
      actionsBg: "#ffffff",
    },
    Tooltip: {
      colorBgDefault: "#3A3A3C",
      colorTextLightSolid: "rgb(221, 221, 221)",
    },
    Message: {
      contentBg: "rgb(36, 36, 38)",
      boxShadow: "      0 2px 16px 0 rgba(0, 0, 0, 0.7)",
    },
    Menu: {
      darkItemSelectedBg: "#438b16",
      radiusItem: 0,
      itemBorderRadius: 0,
      radiusSubMenuItem: 0,
      subMenuItemBorderRadius: 0,
      darkItemBg: "#1C1C1E",
      darkSubMenuItemBg: "#1c1c1",
      colorBgContainer: "#1C1C1E",
      colorBgElevated: "#1C1C1E",
      darkItemHoverColor: "#5c9931",
      darkItemColor: "rgba(255,255,255,0.85)",
      marginXXS: "0px",
    },
    Dropdown: {
      controlItemBgActiveHover: "rgb(43, 43, 47)",
      controlItemBgHover: "rgb(43, 43, 47)",
      controlItemBgActive: "rgb(56, 56, 59)",
      colorBgElevated: "rgb(28, 28, 30)",
      colorError: "#D95A50",
      colorPrimary: "rgb(221, 221, 221)",
      colorSplit: "rgb(68, 68, 71)",
      colorTextDisabled: "#545456",
      boxShadowSecondary: "      0 2px 16px 0 rgba(0, 0, 0, 0.7)",
      colorTextDescription: "rgb(175, 175, 176)",
      colorText: "rgb(221, 221, 221)",
      colorPrimaryBorder: "rgb(72, 72, 74)",
    },
    Steps: {
      colorBorderBg: "transparent",
      colorBorderSecondary: "transparent",
      wireframe: true,
      colorError: "rgb(217, 90, 80)",
      colorPrimary: "rgb(67, 139, 22)",
      colorSplit: "rgb(68, 68, 71)",
    },
    Breadcrumb: {
      itemColor: "rgb(175, 175, 176)",
      lastItemColor: "#DDDDDD",
      linkColor: "rgb(175, 175, 176)",
      linkHoverColor: "#DDDDDD",
      separatorColor: "rgb(175, 175, 176)",
    },
    Cascader: {
      controlItemBgHover: "rgb(43, 43, 47)",
      controlItemBgActive: "rgb(56, 56, 59)",
      colorBgContainerDisabled: "rgb(43, 43, 47)",
      colorSplit: "rgb(68, 68, 71)",
      colorText: "rgb(175, 175, 176)",
      colorTextDescription: "rgb(107, 107, 108)",
      colorTextDisabled: "rgb(84, 84, 86)",
      colorPrimary: "rgb(67, 139, 22)",
      colorHighlight: "rgb(219, 126, 26)",
    },
    Mentions: {
      controlItemBgHover: "rgb(43, 43, 47)",
      colorBgElevated: "rgb(36, 36, 38)",
      colorBgContainerDisabled: "#2B2B2F",
      controlOutline: "#76A650",
      activeBg: "rgba(255, 255, 255, 0)",
      hoverBg: "rgba(255, 255, 255, 0.01)",
      activeBorderColor: "rgb(67, 139, 22)",
    },
    DatePicker: {
      colorBgContainer: "transparent",
      controlItemBgHover: "#3A3A3C",
      controlItemBgActive: "#4A4A4D",
      colorErrorOutline: "#e6847a",
      colorIconHover: "#dddddd",
      colorBgContainerDisabled: "#2B2B2F",
      controlOutline: "#76A650",
      controlOutlineWidth: 1,
      colorWarningOutline: "#e89d41",
      activeShadow: "0 2 16 0px rgba(0, 0, 0, 0.7)",
      warningActiveShadow: "0 2 16 0px rgba(0, 0, 0, 0.7)",
      errorActiveShadow: "0 2 16 0px rgba(0, 0, 0, 0.7)",
      cellBgDisabled: "rgb(43, 43, 47)",
      activeBorderColor: "rgb(67, 139, 22)",
      colorBgElevated: "rgb(36, 36, 38)",
    },
    Pagination: {
      controlOutline: "#76A650",
      controlOutlineWidth: 1,
      controlItemBgActiveDisabled: "#2B2B2F",
      colorBgContainerDisabled: "rgb(43, 43, 47)",
      wireframe: true,
      itemActiveBgDisabled: "rgb(43, 43, 47)",
      itemActiveColorDisabled: "rgb(84, 84, 86)",
      itemBg: "rgba(255, 255, 255, 0)",
      itemInputBg: "rgba(255, 255, 255, 0)",
      itemLinkBg: "rgba(255, 255, 255, 0)",
      colorBgContainer: "rgba(28, 28, 30, 0)",
    },
    TreeSelect: {
      colorBgContainerDisabled: "#2B2B2F",
      colorBgElevated: "rgb(36, 36, 38)",
      colorTextDisabled: "#545456",
      controlItemBgActive: "rgb(56, 56, 59)",
      controlItemBgHover: "rgb(43, 43, 47)",
      colorTextLightSolid: "#DDD",
      colorBorder: "transparent",
    },
    Form: {
      controlOutline: "#76A650",
      controlOutlineWidth: 1,
      colorPrimary: "#438B16",
      colorSuccess: "#438B16",
      colorError: "#D95A50",
      colorWarning: "#DB7E1A",
      colorTextDescription: "#6B6B6C",
    },
    Tabs: {
      cardBg: "rgba(0, 0, 0, 0.02)",
      colorBgContainer: "transparent",
      colorPrimary: "#438B16",
      colorTextDisabled: "#545456",
      inkBarColor: "#438B16",
    },
    Progress: {
      colorError: "#D95A50",
      colorInfo: "#1973D4",
      colorSuccess: "#438B16",
      colorFillSecondary: "#3A3A3C",
      defaultColor: "rgb(67, 139, 22)",
      remainingColor: "rgb(56, 56, 59)",
    },
    Result: {
      colorError: "#D95A50",
      colorInfo: "#1973D4",
      colorSuccess: "#438B16",
      colorWarning: "#DB7E1A",
    },
    Spin: {
      colorPrimary: "#438B16",
    },
    Anchor: {
      colorPrimary: "#438B16",
      colorSplit: "rgb(68, 68, 71)",
    },
    Typography: {
      colorError: "#D95A50",
      colorLink: "#1973D4",
      colorLinkHover: "#3f92e0",
      colorLinkActive: "#0c55ad",
      colorErrorHover: "#e6847a",
      colorSuccess: "#438B16",
      colorWarning: "#DB7E1A",
    },
    Tree: {
      directoryNodeSelectedBg: "rgb(67, 139, 22)",
    },
    Popover: {
      colorBgElevated: "rgb(28, 28, 30)",
      boxShadowSecondary: "0 2px 16px 0 rgba(0, 0, 0, 0.6)",
    },
    Divider: {
      colorSplit: "rgb(68, 68, 71)",
    },
    Statistic: {
      colorTextHeading: "rgb(221, 221, 221)",
    },
    Checkbox: {
      colorBgContainerDisabled: "rgb(43, 43, 47)",
    },
    Radio: {
      colorPrimaryActive: "rgba(43, 102, 11, 0)",
    },
    Switch: {
      handleBg: "rgb(221, 221, 221)",
    },
    Input: {
      colorFillAlter: "rgb(43, 43, 47)",
      activeBorderColor: "rgb(67, 139, 22)",
    },
    InputNumber: {
      handleBorderColor: "rgb(72, 72, 74)",
      colorBgContainerDisabled: "rgb(43, 43, 47)",
    },
    Transfer: {
      colorBorder: "rgb(72, 72, 74)",
      colorBgContainerDisabled: "rgb(43, 43, 47)",
      colorSplit: "rgb(68, 68, 71)",
      controlItemBgHover: "rgb(43, 43, 47)",
    },
    Upload: {
      colorFillAlter: "rgb(19, 19, 20)",
    },
    Image: {
      colorBgContainerDisabled: "rgb(43, 43, 47)",
    },
    List: {
      colorSplit: "rgb(43, 43, 47)",
    },
    Tag: {
      lineWidth: 0,
    },
  },
};

export default THEME_DARK;
