import type { MenuProps } from "antd";
import type { SideMenu } from "#/public";
import type { AppDispatch } from "@/stores";
import { useCallback, useEffect, useState } from "react";
import { Menu } from "antd";
import { Icon } from "@iconify/react";
import { setTitle } from "@/utils/helper";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useCommonStore } from "@/hooks/useCommonStore";
import { useNavigate, useLocation } from "react-router-dom";
import { addTabs, setNav, setActiveKey } from "@/stores/tabs";
import { setOpenKeys, setSelectedKeys, toggleCollapsed } from "@/stores/menu";
import {
  filterMenus,
  getFirstMenu,
  getMenuByKey,
  getMenuName,
  getOpenMenuByRouter,
  handleFilterMenus,
  splitPath,
} from "@/menus/utils/helper";
import styles from "../index.module.less";
import Logo from "@/assets/images/logo.png";

function LayoutMenu() {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { pathname } = useLocation();
  const dispatch: AppDispatch = useDispatch();
  const [menus, setMenus] = useState<SideMenu[]>([]);
  // 获取当前语言
  const currentLanguage = i18n.language;

  const {
    isMaximize,
    isCollapsed,
    isPhone,
    openKeys,
    selectedKeys,
    permissions,
    menuList,
  } = useCommonStore();

  // 处理默认展开
  useEffect(() => {
    const newOpenKey = getOpenMenuByRouter(pathname);
    if (!isPhone && !isCollapsed) {
      dispatch(setOpenKeys(newOpenKey));
      dispatch(setSelectedKeys(pathname));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  /**
   * 设置浏览器标签
   * @param list - 菜单列表
   * @param path - 路径
   */
  const handleSetTitle = useCallback((list: SideMenu[], path: string) => {
    const title = getMenuName(list, path, i18n.language);
    if (title) setTitle(t, title);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    handleSetTitle(menuList, pathname);
  }, [pathname, menuList, handleSetTitle]);

  /**
   * 转换菜单icon格式
   * @param menus - 菜单
   */
  const filterMenuIcon = useCallback((menus: SideMenu[]) => {
    for (let i = 0; i < menus.length; i++) {
      if (menus[i]?.icon) {
        menus[i].icon = <Icon icon={menus[i].icon as string} />;
      }

      if (menus[i]?.children?.length) {
        filterMenuIcon(menus[i].children as SideMenu[]);
      }
    }
  }, []);

  // 过滤没权限菜单
  useEffect(() => {
    if (permissions.length > 0) {
      const newMenus = filterMenus(menuList, permissions);
      filterMenuIcon(newMenus);
      setMenus(newMenus || []);
    }
  }, [filterMenuIcon, permissions, currentLanguage, menuList]);

  /**
   * 处理跳转
   * @param path - 路径
   */
  const goPath = (path: string) => {
    navigate(path);
    const menuByKeyProps = { menus, permissions, key: path };
    const newTab = getMenuByKey(menuByKeyProps);
    if (newTab) {
      dispatch(setActiveKey(newTab.key));
      dispatch(setNav(newTab.nav));
      dispatch(addTabs(newTab));
    }
  };

  /**
   * 点击菜单
   * @param e - 菜单事件
   */
  const onClick: MenuProps["onClick"] = (e) => {
    goPath(e.key);
    if (isPhone) hiddenMenu();
  };

  /**
   * 对比当前展开目录是否是同一层级
   * @param arr - 当前展开目录
   * @param lastArr - 最后展开的目录
   */
  const diffOpenMenu = (arr: string[], lastArr: string[]) => {
    let result = true;

    for (let j = 0; j < arr.length; j++) {
      if (arr[j] !== lastArr[j]) {
        result = false;
        break;
      }
    }

    return result;
  };

  /**
   * 展开/关闭回调
   * @param openKeys - 展开键值
   */
  const onOpenChange = (openKeys: string[]) => {
    const newOpenKey: string[] = [];
    let last = ""; // 最后一个目录结构

    // 当目录有展开值
    if (openKeys.length > 0) {
      last = openKeys[openKeys.length - 1];
      const lastArr: string[] = splitPath(last);
      newOpenKey.push(last);

      // 对比当前展开目录是否是同一层级
      for (let i = openKeys.length - 2; i >= 0; i--) {
        const arr = splitPath(openKeys[i]);
        const hasOpenKey = diffOpenMenu(arr, lastArr);
        if (hasOpenKey) newOpenKey.unshift(openKeys[i]);
      }
    }

    dispatch(setOpenKeys(newOpenKey));
  };

  /** 点击logo */
  const onClickLogo = () => {
    const firstMenu = getFirstMenu(menus, permissions);
    goPath(firstMenu);
    if (isPhone) hiddenMenu();
  };

  /** 隐藏菜单 */
  const hiddenMenu = () => {
    dispatch(toggleCollapsed(true));
  };

  return (
    <>
      <div
        id="menu"
        className={`
          transition-all
          overflow-auto
          z-2
          py-4px
          ${styles.menu}
          ${isCollapsed ? styles.menuClose : ""}
          ${isMaximize || (isPhone && isCollapsed) ? styles.menuNone : ""}
          ${isPhone ? "!z-1002" : ""}
        `}
      >
        <div
          className={`
            text-white
            content-center
            cursor-pointer
            flex
            py-16px
            items-center
            justify-center
          `}
          onClick={onClickLogo}
        >
          <img
            src={Logo}
            width={40}
            height={32}
            // className="object-contain"
            alt="logo"
          />

          {/* <span
            className={`
            text-white
            ml-3
            text-xl
            font-bold
            truncate
             text-center
            ${isCollapsed ? "hidden" : ""}
          `}
          ></span> */}
        </div>
        <div
          className={`
            text-white
            ml-3
            text-xl
            font-bold
            truncate
            text-center
            ${isCollapsed ? "hidden" : ""}
          `}
        >
          {t("public.currentName")}
        </div>
        <div className="py-16px">
          <Menu
            className="z-1000"
            selectedKeys={[selectedKeys]}
            openKeys={openKeys}
            mode="inline"
            theme="dark"
            inlineCollapsed={isCollapsed}
            items={handleFilterMenus(menus)}
            onClick={onClick}
            onOpenChange={onOpenChange}
          />
        </div>
      </div>

      {isPhone && !isCollapsed && (
        <div
          className={`
            ${styles.cover}
            fixed
            w-full
            h-full
            bg-gray-500
            bg-opacity-10
            z-1001
          `}
          onClick={hiddenMenu}
        />
      )}
    </>
  );
}

export default LayoutMenu;
