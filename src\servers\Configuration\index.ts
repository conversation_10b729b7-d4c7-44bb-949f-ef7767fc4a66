/**
 * 大模型测评服务 - 配置管理API接口
 *
 * 本文件包含了系统中各个模块的API接口定义，主要包括：
 * - 数据总览相关接口
 * - 字典管理相关接口
 * - 用例管理相关接口
 * - 策略管理相关接口
 * - 敏感词库管理相关接口
 *
 * <AUTHOR>
 * @version 5.0
 * @since 2024
 */

import { request } from '@/servers/request';

/** API基础路径 */
const API_BASE = "/api"

/**
 * 通用查询参数接口
 * 用于分页查询和关键词搜索
 */
interface QueryParams {
  /** 每页显示数量 */
  limit: number,
  /** 偏移量，用于分页 */
  offset: number,
  /** 搜索关键词 */
  keyword: string,
}

/* ==================== 数据总览相关接口 ==================== */

/**
 * 获取数据总览数据
 * 用于仪表板页面显示系统整体数据统计信息
 *
 * @param data - 查询参数对象
 * @returns Promise<any> 返回包含数据趋势信息的响应
 */
export function getDataTrends(data: object) {
  return request.get('/dashboard', { params: data });
}

/* ==================== 字典管理相关接口 ==================== */

/**
 * 获取字典列表
 * 支持分页查询、关键词搜索、模块筛选等功能
 *
 * @param data - 查询参数
 * @param data.limit - 每页显示数量
 * @param data.offset - 偏移量，用于分页
 * @param data.module - 可选，模块名称筛选
 * @param data.keyword - 搜索关键词
 * @param data.parent_id - 可选，父级字典ID，用于获取子级字典
 * @param options - 可选，额外的请求配置参数
 * @returns Promise<any> 返回字典列表数据
 */
export function getDictList(
  data: {
    "limit": number,
    "offset": number,
    "module"?: string,
    "keyword": string,
    "parent_id"?: string,
  },
  options?: { [key: string]: any },
) {
  return request.post(API_BASE + '/dict/list',
    data
  );
}

/**
 * 获取字典详情
 * 根据字典ID获取具体的字典信息
 *
 * @param id - 字典唯一标识符
 * @returns Promise<any> 返回字典详细信息
 */
export function getDictDetail(
  id: string,
) {
  return request.get(API_BASE + `/dict/${id}`);
}

/**
 * 删除字典
 * 支持强制删除模式，可以删除有关联数据的字典
 *
 * @param id - 要删除的字典ID
 * @param force - 可选，是否强制删除（忽略关联数据检查）
 * @returns Promise<any> 返回删除操作结果
 */
export function removeDict(
  id: string,
  force?: boolean,
) {
  const url = API_BASE + `/dict/${id}`;
  const requestConfig: any = {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  // 如果有 force 参数，作为查询参数传递
  if (force) {
    requestConfig.params = { force };
  }
  return request.delete(url, requestConfig);
}

/**
 * 获取字典树结构
 * 根据父级ID获取字典的树形结构数据
 *
 * @param parent_id - 父级字典ID
 * @returns Promise<any> 返回字典树形结构数据
 */
export function getDictTree(
  parent_id: string,
) {
  const url = API_BASE + `/dict/nodes`;
  return request.post(url, { parent_id });
}
/* ==================== 用例管理相关接口 ==================== */

/**
 * 获取用例列表
 * 支持按分类、关键词、策略等条件筛选用例
 *
 * @param data - 查询参数
 * @param data.limit - 每页显示数量
 * @param data.offset - 偏移量，用于分页
 * @param data.classification_id - 可选，分类ID筛选
 * @param data.keyword - 可选，搜索关键词
 * @param data.strategy_id - 可选，策略ID筛选
 * @returns Promise<any> 返回用例列表数据
 */
export function getUseCaseList(
  data: {
    limit: number,
    offset: number,
    classification_id?: string,
    keyword?: string,
    strategy_id?: string,
  },
) {
  return request.post(API_BASE + '/usecase/list', data
  );
}

/**
 * 获取用例详情
 * 根据用例ID获取具体的用例信息
 *
 * @param params - 参数对象
 * @param params.id - 用例唯一标识符
 * @returns Promise<any> 返回用例详细信息
 */
export function getUseCaseDetail(
  params: { id: string },
) {
  const url = API_BASE + `/usecase/${params.id}`;
  return request.get(url);
}

/**
 * 创建用例
 * 创建新的安全检测用例
 *
 * @param body - 用例数据
 * @param body.payload - 用例载荷数据数组
 * @param body.evaluate_method - 评估方法配置数组
 * @param body.classfications - 分类信息数组
 * @param options - 可选，额外的请求配置参数
 * @returns Promise<any> 返回创建结果
 */
export function createUseCase(
  body: {
    payload: string[],
    evaluate_method: API.EvaluateMethod[],
    classfications: API.Classifications[],
  },
  options?: { [key: string]: any },
) {
  return request.post(API_BASE + '/usecase/create',
    body);
}

/**
 * 更新用例
 * 更新现有用例的信息
 *
 * @param data - 用例更新数据
 * @param data.id - 用例ID
 * @param data.payload - 用例载荷数据数组
 * @param data.evaluate_method - 评估方法配置数组
 * @param data.classfications - 分类信息数组
 * @param options - 可选，额外的请求配置参数
 * @returns Promise<any> 返回更新结果
 */
export function updateUseCase(
  data: {
    id: string,
    payload: string[],
    evaluate_method: API.EvaluateMethod[],
    classfications: API.Classifications[],
  },
  options?: { [key: string]: any },
) {
  const url = API_BASE + `/usecase/${data.id}`;
  return request.put(url, data);
}

/**
 * 删除用例
 * 根据用例ID删除指定用例
 *
 * @param options - 参数对象
 * @param options.id - 要删除的用例ID
 * @returns Promise<any> 返回删除操作结果
 */
export function removeUseCase(options: { id: string }) {
  const url = API_BASE + `/usecase/${options.id}`;
  return request.delete(url);
}

/**
 * 获取统计数据
 * 获取指定字典的统计信息，如使用次数、关联数据等
 *
 * @param id - 字典ID
 * @returns Promise<any> 返回字典统计数据
 */
export function getStatistics(
  id: string,
) {
  const url = API_BASE + `/dict/statistics/${id}`;
  return request.get(url);
}

/**
 * 更新字典详情
 * 更新指定字典的详细信息
 *
 * @param id - 字典ID
 * @param data - 要更新的字典数据
 * @returns Promise<any> 返回更新操作结果
 */
export function updateDictDetail(id: string, data: any) {
  return request.put(API_BASE + `/dict/${id}`, data);
}

/* ==================== 策略管理相关接口 ==================== */

/**
 * 获取策略列表
 * 支持分页查询、排序和筛选功能
 *
 * @param params - 查询参数（分页和关键词）
 * @param sort - 排序配置对象
 * @param filter - 筛选条件对象
 * @returns Promise<any> 返回策略列表数据
 */
export function getPolicyList(
  params: QueryParams,
  sort: Record<string, any>,
  filter: Record<string, any>,
) {
  return request.post(API_BASE + '/strategy/list', params)
}

/**
 * 获取策略详情
 * 根据策略ID获取具体的策略信息
 *
 * @param id - 策略唯一标识符
 * @returns Promise<API.StrategyItem> 返回策略详细信息
 */
export function getPolicy(
  id: string
) {
  return request.get<API.StrategyItem>(API_BASE + '/strategy/' + id)
}

/**
 * 添加策略
 * 创建新的安全检测策略
 *
 * @param data - 策略数据对象
 * @returns Promise<any> 返回创建结果
 */
export function addStrategy(data: any) {
  return request.post(API_BASE + '/strategy/create', data);
}

/**
 * 将策略关联到用例
 * 支持批量关联策略到多个用例或分类
 *
 * @param data - 关联数据
 * @param data.usecase_id_list - 可选，用例ID列表
 * @param data.classification_id_list - 可选，分类ID列表
 * @param data.strategy_id - 策略ID
 * @returns Promise<Record<string, any>> 返回关联操作结果
 */
export function addStrategyToUsecase(
  data: {
    usecase_id_list?: any[];
    classification_id_list?: any[];
    strategy_id: string;
  },
) {
  return request.post<Record<string, any>>(API_BASE + '/strategy/usecase', data);
}

/**
 * 删除策略
 * 支持强制删除模式，可以删除有关联数据的策略
 *
 * @param data - 删除参数
 * @param data.id - 要删除的策略ID
 * @param data.force - 可选，是否强制删除（忽略关联数据检查）
 * @returns Promise<Record<string, any>> 返回删除操作结果
 */
export function deleteStrategy(
  data: {
    id: string,
    force?: boolean,
  },
) {
  const url = API_BASE + `/strategy/delete`;
  return request.delete<Record<string, any>>(url, data);
}


/* ==================== 敏感词库管理相关接口 ==================== */

/**
 * 获取词库列表
 * 支持分页查询和关键词搜索
 *
 * @param data - 查询参数（分页和关键词）
 * @returns Promise<API.LexiconList> 返回词库列表数据
 */
export function getLexiconList(
  data: QueryParams,
) {
  return request.post<API.LexiconList>(API_BASE + '/sensitive/lexicon/list',
    data);
}

/**
 * 删除词库
 * 根据词库ID删除指定的敏感词库
 *
 * @param id - 词库ID（数字或字符串类型）
 * @returns Promise<any> 返回删除操作结果
 */
export function deleteLexicon(id: number | string) {
  return request.delete(API_BASE + `/sensitive/lexicon/${id}`);
}

/**
 * 创建词库
 * 创建新的敏感词库
 *
 * @param data - 词库数据
 * @param data.name - 词库名称
 * @param data.description - 可选，词库描述
 * @param data.tag - 可选，词库标签数组
 * @returns Promise<any> 返回创建结果
 */
export function createLexicon(data: {
  name: string;
  description?: string;
  tag?: string[];
}) {
  return request.post(API_BASE + '/sensitive/lexicon/create',
    data);
}

/**
 * 更新词库
 * 更新现有词库的信息
 *
 * @param id - 词库ID（数字或字符串类型）
 * @param data - 要更新的词库数据
 * @param data.name - 可选，词库名称
 * @param data.description - 可选，词库描述
 * @param data.tag - 可选，词库标签数组
 * @returns Promise<any> 返回更新结果
 */
export function updateLexicon(id: number | string, data: {
  name?: string;
  description?: string;
  tag?: string[];
}) {
  return request.put(API_BASE + `/sensitive/lexicon/${id}`,
    data,
  );
}

/**
 * 添加词条到词库
 * 将指定的词条批量添加到词库中
 *
 * @param data - 添加词条的数据
 * @param data.word_id_list - 可选，词条ID列表
 * @param data.lexicon_id - 目标词库ID
 * @param data.rule_class - 可选，规则分类
 * @param data.tag - 可选，词条标签
 * @returns Promise<any> 返回添加操作结果
 */
export function addWordToLexicon(data: {
  word_id_list?: string[];
  lexicon_id: string;
  rule_class?: string;
  tag?: string;
}) {
  return request.post(API_BASE + '/sensitive/lexicon/addword',
    data,
  );
}

/**
 * 获取词条列表
 * 支持多种筛选条件的词条查询
 *
 * @param data - 查询参数
 * @param data.limit - 每页显示数量
 * @param data.offset - 偏移量，用于分页
 * @param data.keyword - 可选，搜索关键词
 * @param data.lexicon_id - 可选，词库ID筛选
 * @param data.rule_class - 可选，规则分类筛选
 * @param data.tag - 可选，标签筛选
 * @returns Promise<API.WordList> 返回词条列表数据
 */
export function getWordList(data: {
  limit: number;
  offset: number;
  keyword?: string;
  lexicon_id?: string;
  rule_class?: string;
  tag?: string;
}) {
  return request.post<API.WordList>(API_BASE + '/sensitive/lexicon/words',
    data,
  );
}

/**
 * 获取词条分类列表
 * 获取系统中所有词条分类的树形结构
 *
 * @returns Promise<API.WordCategoryList> 返回词条分类树形数据
 */
export function getWordCategoryList() {
  return request.get<API.WordCategoryList>(API_BASE + '/sensitive/lexicon/tree');
}