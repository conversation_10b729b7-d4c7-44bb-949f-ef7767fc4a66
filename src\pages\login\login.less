@import url("@/assets/css/default.less");
// 登录页面样式
.login-title {
  position: fixed;
  top: 0px;
  box-shadow: 0px 2px 8px 0px @shadowColor;
  height: 64px;
  width: 100vw;
}
.login-content {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  .login-logo {
    height: 28px;
    padding: 18px 48px;
  }
}
.login-body {
  position: absolute;
  min-height: 640px;
  top: 50%;
  min-width: 520px;
  margin-top: calc(100vh * (-315 / 900));
  margin-left: calc(100vw * (-730 / 1920));
  left: 50%;
  display: flex;
  border-radius: 10px;
  width: calc(100vw * (1360 / 1920));
  height: calc(100vh * (640 / 900));
  border-radius: 12px;
  box-shadow: 0px 6px 24px 0px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(8px);
  .login-text {
    padding: 13% 48px;
    font-weight: medium;
    width: 60%;
    .text-name {
      font-size: calc(100vw * (40 / 1920));
      line-height: calc(100vw * (120 / 1920));
      height: calc(100vw * (120 / 1920));
      font-family: PingFangSC;
    }
    .text-info {
      font-size: calc(100vw * (20 / 1920));
      color: @colorText;
      line-height: calc(100vw * (68 / 1920));
      font-family: HelveticaNeue;
      span {
        color: #55a722;
      }
    }
    .text-arow {
      width: 20px;
      margin-top:15%
    }
  }
  .login-box {
    width: 37%;
    min-width: 520px;
    padding: 9% 48px;
    border-radius: 0px 12px 12px 0px;
    background: #ffffff;
    .box-title {
      line-height: calc(100vw * (36 / 1920));
      margin-bottom: 48px;
      font-size: 28px;
      color: #535353;
      font-weight: 500;
    }
  }
}
.login-footer {
  position: fixed;
  bottom: 0px;
  height: 60px;
  width: 100vw;
  text-align: center;
  line-height: 60px;
  font-size: 14px;
  color: #7E7E7E;
}

// 浅色
.login-bg-light {
  background: url("@/assets/images/login/bg.png") no-repeat;
  background-size: cover;
}
.footer-light {
  background: #f0f1f3;
  border: none;
}

// 暗黑
.login-bg-dark {
  background: url("@/assets/images/login/bg-dark.png") no-repeat;
  background-size: cover;
}
.footer-dark {
  background: none;
  border-top: 1px solid rgba(222,224,227,0.2);
}
