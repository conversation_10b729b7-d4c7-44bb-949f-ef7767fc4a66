.title-with-vertical-divider {
    display: flex;
    align-items: center;
    padding-left: 16px; /* 给分隔线留出空间 */
    position: relative;

    &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 6px;
        height: 24px;
        background-color: #67c23a; /* 绿色 */
        box-shadow: 0 0 8px rgba(103, 194, 58, 0.5); /* 绿色阴影 */
    }
}
//1.1字体
.custom-paragraph {
    font-family: 'PingFang SC', 'PingFang SC-Regular';
    font-weight: 400; // 'Regular' 对应字体粗细 400
    text-align: left;
    color: #535353;
  }