.ant-tabs-top>.ant-tabs-nav,
.ant-tabs-bottom>.ant-tabs-nav,
.ant-tabs-top>div>.ant-tabs-nav,
.ant-tabs-bottom>div>.ant-tabs-nav {
  margin: 0 0 10 0;
}

.ant-typography ul {
  list-style-type: circle !important;
}

// .global-header {
//   background-color: white;
//   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
//   border-bottom: 1px solid #f0f0f0;
//   .header-flex {
//     display: flex;
//     justify-content: space-between;
//     align-items: center; /* 让内容垂直居中 */
//   }
// }
//顶部样式
.global-header {
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #f0f0f0;
  position: sticky; // 固定在顶部
  top: 0;
  left: 0;
  right: 0;
  // z-index: 1000; // 确保在其他内容之上
  // height: 64px; 
    .header-flex {
    display: flex;
    justify-content: space-between;
    align-items: center; /* 让内容垂直居中 */
  }
}

.llm-report-content {
  padding-top: 64px; // 与 Header 高度一致，避免内容被遮挡
}
.header-left h1 {
  color: black;
}

//更改切换按钮样式
.toggle-sider-btn {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  width: 24px; 
  height: 40px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 0 4px 4px 0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: left 0.3s ease;
  z-index: 100;
}

.sider-visible .toggle-sider-btn {
  left: 230px;
  /* 与 Sider 宽度一致 */
}

.sider-hidden .toggle-sider-btn {
  left: 0;
}
/* 更改图标按钮宽度 */
:where(.css-dev-only-do-not-override-t4hacb).ant-btn.ant-btn-icon-only {
  width: 24px;
}
.menu-title {
  font-weight: bold;
  font-size: 18px;
  /* 增大字体大小 */

  cursor: default;
}

.ant-card-head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: nowrap;
  /* 防止内容换行 */
}

.ant-card-head-title {
  white-space: nowrap;
  /* 标题不换行 */
  overflow: hidden;
  text-overflow: ellipsis;
  /* 标题过长时显示省略号 */
  flex-grow: 1;
  /* 让标题占据剩余空间 */
}

.ant-card-extra {
  flex-shrink: 0;
  /* 防止按钮被压缩 */
}

.thick-divider {
  border-left-width: 3px; // 调整边框宽度，数值越大分割线越粗
}

/* 定义大数字样式 */
.title-large-number {
  font-size: 55px;
  font-weight: bold;
  display: block;
  color: #55a722; // 设置大数字颜色为绿色
}

/* 定义副标题样式 */
.title-subtext {
  font-size: 20px;
  display: block;
}

// 切换卡片展开状态按钮的样式
:where(.css-dev-only-do-not-override-mc1tut).ant-btn-color-link.ant-btn-variant-link.toggle-card-btn {
  color: #55a722; // 设置按钮文字颜色为绿色

  // 设置图标颜色为绿色
  & svg {
    fill: #55a722;
  }

}

.ant-btn-primary {
  color: #fff;
  background: #55a722;
  box-shadow: 0 2px 0 rgba(55, 99, 11, 0.16);
}
// 定义结论卡片的样式
.recommendation-card {
  background-color: #f0f9eb;
  border-radius: 8px;
  padding: 16px;
}
.recommendation-intro {
  color: gray; // 设置字体颜色为灰色
  margin-bottom: 12px; // 设置底部边距，与其他列表项保持间距
  list-style-type: none; // 移除列表项标记
  margin-left: -20px; // 消除因 padding-left 导致的缩进
}


.recommendation-list {
  /* 恢复默认列表样式 */
  list-style-type: disc; 
  /* 调整左侧内边距，让列表项显示更美观 */
  padding-left: 20px; 
}

.recommendation-item {
  /* 移除 flex 布局 */
  display: list-item; 
  margin-bottom: 12px;
}

.recommendation-item:last-child {
  margin-bottom: 0;
}

// 引入背景图片
.background-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 30%; // 占满 Content 高度的 30%
  z-index: 0;
}

.content-overlay {
  position: relative;
  z-index: 1; // 确保内容显示在背景之上
}

//ProCard 样式
.header-procard {
  background: #f7f8fa;
  border-radius: 0px;
}

.content-procard {
  color: #535353;
}

//下载按钮
.download-report-btn {
  background: #55a722;
  border-color: #55a722;
}

.download-report-btn:hover {
  background: #45851b;
  border-color: #45851b;
}
:where(.css-dev-only-do-not-override-mc1tut).ant-btn-primary.download-report-btn:not(:disabled):not(.ant-btn-disabled):hover {
  background: #45851b;
  border-color: #45851b;
  color: #fff;
}
//左边菜单
// .fixed-sider {
//   position: fixed;
//   top: 0;
//   left: 0;
//   height: 100vh;
//   z-index: 100; // 确保菜单显示在其他内容上方
// }

// // 调整右侧内容的左边距，避免与固定菜单重叠
// .llm-report-content {
//   padding-left: 230px; // 与菜单宽度一致
// }

// // 当菜单隐藏时，减少右侧内容的左边距
// .sider-hidden + .llm-report-content {
//   padding-left: 0;
// }

