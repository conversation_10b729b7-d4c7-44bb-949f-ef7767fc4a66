import { useState, useEffect, useRef } from 'react';

import { useParams } from '@umijs/max';

import TC260Report from './TC260';
import { ConfigProvider } from 'antd';
import Settings from '../../../config/defaultSettings';
// config/defaultSettings';

export default () => {
    const { taskId } = useParams();
    return (
        <ConfigProvider
            theme={{
                token: {
                    colorPrimary: Settings.token?.colorPrimary || '#55a722',
                },
            }}
        >

            <TC260Report taskId={taskId}></TC260Report>
        </ConfigProvider>

    )
}



