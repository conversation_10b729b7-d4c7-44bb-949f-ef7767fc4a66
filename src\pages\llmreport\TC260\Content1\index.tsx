import { Typography, Space, Divider } from 'antd';
const { Title, Paragraph, Text } = Typography;
import { ProCard, StatisticCard } from '@ant-design/pro-components';
import './index.less'; // 引入 less 文件
import iconmodel from '@/static/assets/iconmodel.png';
const Content1 = (props: any) => {
    const { task, testModel, scanPolicy } = props
    return (
        <>
            <Divider></Divider>
            <div id="section1.1">
                <Title
                    level={3}
                    className="title-with-vertical-divider"
                >
                    1.1 评估工具介绍
                </Title>
                <Paragraph className="custom-paragraph">

                    大模型安全合规评估工具由绿盟科技（NSFOCUS）自主研发，旨在评估目标大模型是否满足特定法律法规的合规需求。绿盟科技作为网络安全领域的领军企业，一直致力于为客户提供全面、可靠的安全解决方案。通过使用该工具，用户可以系统地扫描和评估大模型的安全性，发现潜在的安全漏洞和风险，并提供针对性的改进建议，以帮助加强大模型的安全防护能力，确保其在实际应用中的合规性、安全性和可靠性。
                    本评估工具不仅支持中外法律法规的合规性评估，还具备灵活评估不同维度的特点。无论是数据隐私保护、知识产权合规，还是模型能力评估、风险评估等方面，该工具都能提供全面、细致的评估报告。能够发现并处理潜在的安全威胁，保障大模型的安全稳定，确保业务顺利发展、稳健、可靠！
                </Paragraph>
            </div>
            <Divider></Divider>
            <div id="section1.2">
                <Title
                    level={3}
                    className="title-with-vertical-divider"
                >
                    1.2 测试模型基本信息
                </Title>
                <Paragraph>
                    {/* //基本信息布局 */}
                    <ProCard
                        split='horizontal'
                        bordered
                        headerBordered
                    >
                        <ProCard>
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                <img src={iconmodel} style={{ width: '8%', height: '8%' }}></img>
                                <Space direction="vertical" style={{ marginLeft: '30px' }}>
                                    <Text strong style={{ fontSize: '18px' }}>测试模型</Text>
                                    <Text style={{ fontSize: '16px' }}>{testModel?.name}</Text>
                                    <Text type="secondary" style={{ fontSize: '16px' }}>{testModel?.api_url}</Text>
                                </Space>
                            </div>
                        </ProCard>
                        <ProCard split='vertical'>
                            <ProCard split='horizontal' colSpan="13%">
                                <ProCard className="header-procard">模型</ProCard>
                                <ProCard className="header-procard">策略</ProCard>
                                <ProCard className="header-procard">评估时间</ProCard>
                                <ProCard className="header-procard">测试题库</ProCard>
                            </ProCard>
                            <ProCard split='horizontal'>
                                <ProCard className="content-procard">{testModel?.name}</ProCard>
                                <ProCard className="content-procard">{scanPolicy.name}</ProCard>
                                <ProCard className="content-procard">{task?.create_time?.slice(0, 10)}</ProCard>
                                <ProCard className="content-procard">{task?.status?.total}</ProCard>
                            </ProCard>
                        </ProCard>
                    </ProCard>
                </Paragraph>
            </div>

            <Divider></Divider>
            <div id="section1.3">
                <Title
                    level={3}
                    className="title-with-vertical-divider"
                >
                    1.3 评估委托方信息
                </Title>
                <Paragraph>
                    <Space direction="vertical" size={12}>
                        {/* <Text style={{ color: 'gray' }}>委托单位：无</Text> */}
                        <Text style={{ color: 'gray' }}>评估单位：绿盟科技天枢实验室</Text>
                        {/* <Text style={{ color: 'gray' }}>评估负责人：王小二</Text> */}
                    </Space>

                </Paragraph>
            </div>
        </>
    )
}

export default Content1;
