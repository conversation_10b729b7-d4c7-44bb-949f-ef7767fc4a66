import { useTranslation } from 'react-i18next';
import CopyInput from '@/components/Copy/CopyInput';
import CopyBtn from '@/components/Copy/CopyBtn';
import BasicContent from '@/components/Content/BasicContent';
import { addStrategy, addStrategyToUsecase, deleteStrategy, getPolicyList } from '@/servers/configuration';
import { FilePdfFilled, FilePdfOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  FooterToolbar,
  PageContainer,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Drawer, message, Tabs, Popconfirm } from 'antd';
import React, { useRef, useState } from 'react';
import AddModalForm from './components/AddModalForm';
function ScanStrategy() {
  const { t } = useTranslation();
  const [modalOpen, handleModalOpen] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.LLMItem>({} as API.LLMItem);
  const [selectedRowsState, setSelectedRows] = useState<API.LLMItem[]>([]);
  const [selectedLlmType, setSelectedLlmType] = useState<string>('请选择模型');
  const [selectedTab, setSelectedTab] = useState<string>('tab1');
  const { TabPane } = Tabs;
  const [requestHeaders, setRequestHeaders] = useState([]);

  //模型表格
  const columns: ProColumns<API.StrategyItem>[] = [
    {
      title: '序号',
      dataIndex: 'id',
      valueType: 'indexBorder',
      width: 60,
      hideInSearch: true,
    },
    {
      title: "策略名称",
      dataIndex: 'name',
      search: {
        transform: (value) => ({ name: value }),
      },

    },

    {
      title: "标签",
      dataIndex: 'tags',
      search: false
    },
    {
      title: "用例数",
      dataIndex: 'usecase_count',
      search: false
    },
    {
      title: "描述",
      dataIndex: 'description',
      search: false,
      ellipsis: true,
    },
    {
      title: "创建时间",
      dataIndex: 'create_time',
      valueType: "dateTime",
      search: false
    },

    {
      title: "操作",
      dataIndex: 'option',
      valueType: 'option',
      width: '15%',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            // handleEditClick(record);
            console.log("record:", record);
            setCurrentRow(record);
            setSelectedLlmType(record?.llm_type || 'openai')
            handleModalOpen(true);
          }}
        >
          编辑
        </a>,
        <Popconfirm
          key="config"
          title={`确认删除策略 "${record.name}" 吗？此操作不可逆！`}
          onConfirm={() => {
            handleRemove([record]);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
          okText="确认"
          cancelText="取消"
        >
          <a>
            删除
          </a>
        </Popconfirm>
      ],
    },
  ];
  //用例表格
  const columnsUsecase: ProColumns<API.UsecaseItem>[] = [
    //序号
    {
      title: '序号',
      dataIndex: 'id',
      valueType: 'indexBorder',
      width: 60,
      hideInSearch: true,
    },
    {
      title: 'Prompt',
      dataIndex: 'payload',
      ellipsis: true,
      search: false,
      // search: {
      //   transform: (value) => ({ payload: value }),
      // },
      width: '30%'
    },
    {
      title: '数据集',
      dataIndex: 'classification',
      hideInSearch: true,
      ellipsis: true,
      render: (text, record) => {
        // 显示完整的 path 路径
        if (record.classification && record.classification.path && Array.isArray(record.classification.path)) {
          return record.classification.path.map(item => item.name).join(' > ') + ' > ' + record.classification.name;
        }
        return '-';
      },
    },

  ];
  //添加策略
  const handleAdd = async (fields: API.StrategyItem) => {
    try {
      console.log("fields:", fields);

      // 先调用 addStrategy 创建策略
      const strategyResponse = await addStrategy(fields);

      // 从响应中获取 strategy_id
      const strategyId = strategyResponse.data?.id || '';

      if (!strategyId) {
        throw new Error('未能获取到策略ID');
      }

      // 如果有选中的用例，调用 addStrategyToUsecase
      if (fields.selectedUseCases && fields.selectedUseCases.length > 0) {
        // 从选中的用例中提取 usecase_id_list 和 classification_id_list
        const usecaseIdList = fields.selectedUseCases.map(item => item.id);
        // const classificationIdList = fields.selectedUseCases.map(item => item.classification?.id).filter(Boolean);

        await addStrategyToUsecase({
          usecase_id_list: usecaseIdList,
          // classification_id_list: [...new Set(classificationIdList)], // 去重
          strategy_id: strategyId,
        });
      }

      message.success('添加成功');
      return true;
    } catch (error) {
      console.error('添加失败:', error);
      message.error('添加失败，请重试！');
      return false;
    }
  };

  //更新模型
  const handleUpdate = async (fields: any) => {
    try {
      console.log("fields:", fields);
      if (fields.template_option) { fields.template_option.headers = [...requestHeaders] }
      await updateTestLLM(fields.id, fields);
      message.success('更新成功');
      return true;
    } catch (error) {
      message.error('更新失败，请重试！');
      return false;
    }
  };

  //删除策略
  const handleRemove = async (selectedRows: any) => {
    const hide = message.loading('正在删除');
    if (!selectedRows) return true;
    try {
      // 对于单个删除，直接使用第一个模型的ID
      if (selectedRows.length === 1) {
        await deleteStrategy({ id: selectedRows[0].id, force: true });
      } else {
        // 批量删除时，逐个删除
        for (const row of selectedRows) {
          await deleteStrategy({ id: row.id });
        }
      }
      hide();
      message.success('删除成功');
      // 刷新表格数据
      actionRef.current?.reload();
      return true;
    } catch (error) {
      hide();
      message.error('删除失败，请重试！');
      return false;
    }
  };
  return (
    <div className="py-16px px-16px">
      {/* <BasicContent isPermission={true}> */}
        <ProTable<API.LLMItem, API.PageParams>
          actionRef={actionRef}
          rowKey="id"
          search={{
            labelWidth: 120,
          }}
          toolBarRender={() => [
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                setCurrentRow({} as API.LLMItem);
                setRequestHeaders([]);
                setSelectedTab('tab1');
                setSelectedLlmType('openai');
                handleModalOpen(true);
              }}
            >
              <PlusOutlined />创建扫描策略
            </Button>,
          ]}
          request={async (params, sort, filter) => {
            const newParams = {
              ...params,
              limit: params.pageSize || 10,
              offset: (params.current - 1) * (params.pageSize || 10),
              keyword: params.name || '',
            }
            const { status, data ,total} = await getPolicyList(newParams, sort, filter);
            if (status === 200) {
              return {
                data: data || [],
                total: total || 0,
                success: true
              };
            } else {
              message.error('获取扫描策略列表失败');
              return {
                data: [],
                total: 0,
                success: false
              };
            }
          }}
          columns={columns}
          rowSelection={{
            onChange: (_, selectedRows) => {
              setSelectedRows(selectedRows);
            },
          }}
        />
        {selectedRowsState?.length > 0 && (
          <FooterToolbar
            extra={
              <div>
                选择
                <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>{' '}
                项
              </div>
            }
          >
            <Button
              onClick={async () => {
                await handleRemove(selectedRowsState);
                setSelectedRows([]);
                actionRef.current?.reloadAndRest?.();
              }}
              danger
            >
              批量删除
            </Button>
          </FooterToolbar>
        )}

        <AddModalForm
          modalOpen={modalOpen}
          onCancel={() => {
            handleModalOpen(false);
            setCurrentRow({} as API.LLMItem);
            setRequestHeaders([]);
          }}
          onSubmit={async (values) => {
            if (currentRow?.id) {
              const success = await handleUpdate(values);
              if (success) {
                actionRef.current?.reload();
              }
              return success;
            } else {
              const success = await handleAdd(values);
              if (success) {
                actionRef.current?.reload();
              }
              return success;
            }
          }}
          currentRow={currentRow}
          selectedLlmType={selectedLlmType}
          setSelectedLlmType={setSelectedLlmType}
          requestHeaders={requestHeaders}
          // 如果需要传递表格列配置
          columns={columnsUsecase} // 根据需要配置用例列
        />
      {/* </BasicContent> */}
    </div>
  );
}

export default ScanStrategy;
