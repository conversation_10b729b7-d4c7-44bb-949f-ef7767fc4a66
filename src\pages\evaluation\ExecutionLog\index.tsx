import { getMissonList, updateMission } from '@/servers/evaluation';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  PageContainer,
  ProDescriptions,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Drawer, Input, message, Tag, Space, Divider, Typography, Tooltip, Badge, Radio, Select } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

const { Title, Paragraph, Text, Link } = Typography;

/**
 * @en-US Update node
 * @zh-CN 更新节点
 *
 * @param fields
 */
const handleUpdate = async (fields: any) => {
  const hide = message.loading('Configuring');
  try {
    await updateTestLLM({
      name: fields.name,
      desc: fields.desc,
      key: fields.key,
    });
    hide();

    message.success('Configuration is successful');
    return true;
  } catch (error) {
    hide();
    message.error('Configuration failed, please try again!');
    return false;
  }
};



const ExcutionLog: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */

  const [showDetail, setShowDetail] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.TaskItem>();
  const [selectedRowsState, setSelectedRows] = useState<API.TaskItem[]>([]);
  const [selectedResult, setSelectedResult] = useState<boolean>(true);
  const [editReason, setEditReason] = useState<string>(currentRow?.evaluate_result?.extra_data?.reason || '');

  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */

  const columns: ProColumns<API.TaskItem>[] = [

    {
      title: '序号',
      dataIndex: 'id',
      valueType: 'indexBorder',
      width: 60,
      hideInSearch: true,
    },
    // {
    //   title: "ID",
    //   dataIndex: 'id',
    //   search: false,
    //   render: (_, record) => (
    //     <Button type='link' onClick={() => {
    //       setCurrentRow(record);
    //       setShowDetail(true)
    //     }}>{record.id}</Button>
    //   ),

    // },
    {
      title: "测试题ID",
      dataIndex: 'id',
      // search: true,
      hideInSearch: true
    },
    {
      title: "任务名称",
      dataIndex: ["task", "name"],
      width: "10%",
      // render: (_, record) => (
      //   <Space>
      //     {record?.task?.name}
      //   </Space>
      // ),
    },
    {
      title: "测试题目(Prompt)",
      dataIndex: ["usecase", "payload"],
      // valueType: 'textarea',
      width: "20%",
      search: {
        transform: (value: string) => ({
          prompt: value
        }),
      },
      render: (_, record) => {
        const payload = record?.usecase?.payload || '';
        return (
          <Tooltip title={payload}>
            <Text ellipsis={{ tooltip: payload }}>{payload}</Text>
          </Tooltip>
        );
      }
    },
    {
      title: "评估结论",
      dataIndex: 'evaluate_result',
      valueType: 'textarea',
      width: "15%",
      search: false,
      // <Tag color='#108ee9'>{record?.evaluate_result?.score}</Tag>
      render: (_, record) => (
        <Space>
          分数：{record?.evaluate_result?.score} 结论：{record?.evaluate_result?.pass ? <Tag color='#87d068'>通过</Tag> : <Tag color='#f50'>未通过</Tag>}
        </Space>
      ),
    },
    {
      title: "状态",
      dataIndex: 'status',
      width: "10%",
      valueEnum: {
        "": {
          text: '请选择',
        },
        "completed": {
          text: '完成',
          status: 'Success',
        },
        'chatting': {
          text: '测试中',
          status: 'Processing',
        },
        'failed': {
          text: '失败',
          status: 'Error',
        },
      },
    },
    {
      title: "用例分类",
      search: {
        transform: (value) => ({ name: value }),
      },
      // width:360,
      ellipsis: true,
      render: (_, record) => {
        const pathStr = record?.usecase?.classification?.path
          ? record.usecase.classification.path.map(item => item.name).join(' -> ')
          : '';
        return <Tooltip title={pathStr}>{pathStr}</Tooltip>;
      }
    },
    {
      title: "完成时间",
      dataIndex: 'finish_time',
      valueType: 'dateTime',
      search: false,
      width: "10%",
    },

    {
      title: "操作",
      dataIndex: 'option',
      valueType: 'option',
      width: "8%",
      render: (_, record) => [
        <><Button type='link' onClick={() => {
          setCurrentRow(record);
          console.log("record", record);

          setShowDetail(true)
        }}>查看</Button>
        </>
      ],
    },
  ];
  // 在组件中添加保存函数
  const handleSave = async () => {
    try {
      const hide = message.loading('保存中...');

      // 调用更新任务的 API
      await updateMission({
        mission_id: currentRow?.id,
        pass: selectedResult,
        reason: editReason,
      });
      // console.log("保存的结果:", selectedResult, "原因:", editReason, "任务ID:", currentRow?.id);

      hide();
      message.success('保存成功');

      // 保存成功后可以刷新表格数据
      actionRef.current?.reload();
    } catch (error) {
      message.error('保存失败，请重试');
    }
  };
  useEffect(() => {
    if (currentRow) {
      setSelectedResult(currentRow?.evaluate_result?.pass);
      setEditReason(currentRow?.evaluate_result?.extra_data?.reason || '');
    }
  }, [currentRow]);

  return (
    <div className="py-16px px-16px">
      <ProTable<API.MissionItem>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        request = {async (params, sort, filter) => {
          const newParams = {
            ...params,
            limit: params.pageSize || 10,
            offset: params.current,
            keyword: params?.task?.name || '',
          }
          const { data, total, status } = await getMissonList(newParams, sort, filter);
          if (status === 200) {
            return {
              data: data || [],
              success: true,
              total
            }
          }
          return {
            data: [],
            success: false,
            total: 0
          }
        }
        }

        pagination={{
          pageSize: 10,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,

        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />

      <Drawer
        width={800}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
          // 关闭时重置状态
          setSelectedResult(true);
          setEditReason('');
        }}
        closable={true}
        extra={
          <Space>
            <Button
              type="primary"
              onClick={handleSave}

            >
              保存
            </Button>
          </Space>
        }
      >
        {currentRow?.id && (<>
          <ProDescriptions<API.MissionItem>
            column={2}
            title={currentRow?.task?.name + ` - ` + currentRow?.id}

          >
            {/* <ProDescriptions.Item
              label="ID"
            >{currentRow?.id}</ProDescriptions.Item>
            <ProDescriptions.Item
              label="任务"
            >{currentRow?.task?.name}</ProDescriptions.Item> */}

            <ProDescriptions.Item
              label="创建时间"
              valueType="dateTime"

            >{currentRow?.create_time}</ProDescriptions.Item>

            <ProDescriptions.Item
              label="完成时间"
              valueType="dateTime"
            >{currentRow?.finish_time}</ProDescriptions.Item>
          </ProDescriptions>

          <Badge.Ribbon text="评估信息" color='blue' placement="start"></Badge.Ribbon>
          <Divider orientation="left"></Divider>
          <ProDescriptions column={3}>
            <ProDescriptions.Item
              label="评估方式"
            >{currentRow?.task?.evaluate_method?.name}
            </ProDescriptions.Item>
            <ProDescriptions.Item
              label="评估分数"
            >
              <Tag color='#108ee9'>{currentRow?.evaluate_result?.score}</Tag>
            </ProDescriptions.Item>
            <ProDescriptions.Item
              label="评估结论"
            >
              {/* 渲染 Radio 组件供用户选择 T 或 F */}
              <Radio.Group
                value={selectedResult}
                onChange={(e) => setSelectedResult(e.target.value)}
              >
                <Radio value={true}>通过</Radio>
                <Radio value={false}>未通过</Radio>
              </Radio.Group>
            </ProDescriptions.Item>

            <ProDescriptions.Item
              span={2}
              label="原因"
            >
              <Input.TextArea
                value={editReason}
                onChange={(e) => setEditReason(e.target.value)}
                rows={4}
                placeholder="请输入原因..."
              />
            </ProDescriptions.Item>
            {/* <ProDescriptions layout="vertical" column={24}>
              <ProDescriptions.Item span={24}
                label="原因"
              >
             
             {currentRow?.evaluate_result?.extra_data?.reason || ''} 
              </ProDescriptions.Item>
            </ProDescriptions> */}

          </ProDescriptions>

          <Badge.Ribbon text="测试用例" color="green" placement="start"></Badge.Ribbon>
          <Divider orientation="left"></Divider>
          <ProDescriptions column={1}>
            <ProDescriptions.Item
              label="测试例分类"
            > {currentRow?.usecase?.classification?.path?.map(item => item.name).join(' -> ')}</ProDescriptions.Item>
          </ProDescriptions>
          <ProDescriptions layout="vertical" column={24}>
            <ProDescriptions.Item span={24}
              label="问题(Q)"
            >
              <Paragraph>
                <blockquote>{currentRow?.raw_data?.[0]?.request}</blockquote>
              </Paragraph>
            </ProDescriptions.Item>

            <ProDescriptions.Item span={24}
              label="回复(A)"
            > <Paragraph>
                <blockquote>{currentRow?.raw_data?.[0]?.response}</blockquote>
              </Paragraph>
            </ProDescriptions.Item>
          </ProDescriptions>
          <ProDescriptions>
          </ProDescriptions>
        </>
        )}
      </Drawer>
    </div>
  );
};

export default ExcutionLog;
