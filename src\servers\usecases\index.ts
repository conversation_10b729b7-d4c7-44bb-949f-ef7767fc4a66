
import { request } from '@/servers/request';

/** API基础路径 */
const API_BASE = "/api"


//获取字典列表
export  function getDictList(
  body: { 
      "limit": number,
      "offset": number,
      "module"?:string,
      "keyword":string,
      "parent_id"?: string,
   },
  options?: { [key: string]: any },
) {
  const res = await request<API.DictList>('/llmapi/dict/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json', 
    },
    data: {
      ...body,
    },
    ...(options || {}),
  });
  return res;

}

//获取字典详情
export  function getDictDetail(
  id:string,

) {
  
  const url = `/llmapi/dict/${id}`;
  return request<Record<string, any>>(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

//删除字典
export  function removeDict(
  id: string,
  force?: boolean, 
) {
  const url = `/llmapi/dict/${id}`;
  const requestConfig: any = {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  // 如果有 force 参数，作为查询参数传递
  if (force) {
    requestConfig.params = { force };
  }
  
  return request<Record<string, any>>(url, requestConfig);
}


//获取字典树
export  function getDictTree(
  parent_id: string,
) {
  const url = `/llmapi/dict/nodes`;
  return request<API.Category[]>(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json', 
    },
    data: {
      parent_id,
    },
  });
}
//获取用例列表
export  function getUseCaseList(
  body: { 
    limit: number,
    offset: number,
    classification_id?: string,
    keyword?:string,
    strategy_id?: string,
 },
) {
  const res = await request<API.UsecaseList>('/llmapi/usecase/list', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json', 
    },
    data: {
      ...body,
    },
  });
  return res;
}

//获取用例详情
export  function getUseCaseDetail(
  params: { id: string },
) {
  const url = `/llmapi/usecase/${params.id}`;
  return request<{
    status: number;
    data: API.UsecaseItem;
    message?: string;
  }>(url, {
    method: 'GET',
  });
}

//创建用例
export  function createUseCase(
  body: {
    payload: string[],
    evaluate_method: API.EvaluateMethod[],
    classfications: API.Classifications[],
  },
  options?: { [key: string]: any },
) {
  const res = await request<API.UsecaseList>('/llmapi/usecase/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json', 
    },
    data: {
      ...body,
    },
    ...(options || {}),
  });
  return res;
}

//更新用例
export  function updateUseCase(
  body: {
    id: string,
    payload: string[],
    evaluate_method: API.EvaluateMethod[],
    classfications: API.Classifications[],
  },
  options?: { [key: string]: any },
) {
  const url= `/llmapi/usecase/${body.id}`;
  const res = await request<API.UsecaseList>(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json', 
    },
    data: {
      ...body,
    },
    ...(options || {}),
  });
  return res;
}

//删除用例
export  function removeUseCase(options: { id: string }) {
  const url = `/llmapi/usecase/${options.id}`;
  return request<Record<string, any>>(url, {
    method: 'DELETE',
  });
}

//获取统计数据
export  function getStatistics(
  id: string,
) {
  const url = `/llmapi/dict/statistics/${id}`;
  return request(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
