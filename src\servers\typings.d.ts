/* eslint-disable */

declare namespace API {

   // LLM模型列表
   type LLMList = {
    status: number;
    data?: LLMItem[];
    /** 列表的内容总数 */
    total?: number;
    message?: string; 
    success?: boolean;
  };
    // 定义 call_option 类型
    type CallOption = {
      // 模型名称
      model: string;
      // 模型接口地址
      api_url: string;
      // 模型 API 密钥
      api_key?: string;
      // top_p 参数
      top_p?: number;
      // top_k 参数
      top_k?: number;
      // temperature 参数
      temperature?: number;
      // max_tokens 参数
      max_tokens?: number;
      // repetition_penalty 参数
      repetition_penalty?: number;
    };
    // LLM模型模板
    type LLMItem = {
      id: string;//模型id：唯一uuid
      name: string; // 模型名称：deepseek-r1、Model-1.8B-F16.gguf
      description: string; // 模型描述
      model_type: string; // test模型 or evalute模型
      mode: string; //internal (langchaingo) | custom（chat请求组合）
      template_id?: string; // llm_type为template 该值不能为空
      llm_type?: string; // langchaingo内置ollama / openai OR custom template Name
      call_option: CallOption; // 调用参数
      create_time?: string; // 创建时间
      update_time?: string; // 更新时间
      // 新增字段
      api_key: null | string;
      internal_option: InternalOption;
      template_option: TemplateOption;
    };

    // 高级参数
    type InternalOption = {
      /**
       * max_tokens
       */
      max_tokens?: number | null;
      /**
       * 模型：secllm-v1
       */
      model: string;
      /**
       * repetition_penalty
       */
      repetition_penalty?: number | null;
      /**
       * temperature
       */
      temperature?: number | null;
      /**
       * top_k
       */
      top_k?: number | null;
      /**
       * top_p
       */
      top_p?: number | null;
      [property: string]: any;
    };

    type TemplateOption = {
      /**
       * 请求头
       */
      headers: Header[];
      /**
       * api请求体
       */
      request_body: string;
      /**
       * api返回体提取规则
       */
      response_extract_rule: string;
    };

    type Header = {
      key: string;
      value: string;

    };
 
  // LLM模板列表
  type LLMAPITemplateList = {
    data?: LLMAPITemplateItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };
  // LLM模型接口模板
  type LLMAPITemplateItem = {
    id?: string;//模型id：唯一uuid
    name?: string; // 模型名称：deepseek-r1、Model-1.8B-F16.gguf
    api_url?: string; // llm服务地址
    api_key?: string; // llm服务调用的api_key
    temperature?: number; // temperature
    top_p?: number; // top_p
    max_tokens?: number; // max_tokens
    datetime?: string; // 添加时间
    api_type?: string; // 类型APITypeOpenAI、APITypeAzure、APITypeAzureAD、custom
    api_version?: string; // required when APIType is APITypeAzure or APITypeAzureAD
    embedding_model?: string; // required when APIType is APITypeAzure or APITypeAzureAD
    description?: string; // 模型描述
    create_time?: string; // 创建时间
    update_time?: string; // 更新时间
    model_type?: string; // test模型 or evalute模型
    request_body?: string; // 请求体
    response_extract_rule?: string; // 响应体提取规则
  };
}