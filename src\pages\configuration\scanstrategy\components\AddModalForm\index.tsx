import React, { useState, useRef, useEffect } from 'react';
import { message, Tree, Tabs, Row, Col, Input, Layout, Form, Card, Button, Steps, Tag } from 'antd';
import { PlusOutlined, ProjectOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-table';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import { getDictList, getDictTree, getDictDetail,getUseCaseList } from '@/servers/configuration';
import { ModalForm,  ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { Typography } from 'antd';

const { Content, Sider } = Layout;
const { Paragraph } = Typography;

interface TreeDataNode {
  title: string;
  key: string;
  children?: TreeDataNode[];
  isLeaf?: boolean;
  raw?: any;
}

interface AddModalFormProps {
  modalOpen: boolean;
  onCancel: () => void;
  onSubmit: (values: any) => Promise<boolean>;
  currentRow?: API.StrategyItem;
  selectedLlmType: string;
  setSelectedLlmType: (type: string) => void;
  requestHeaders: any[];
  // setRequestHeaders: (headers: any[]) => void;
  onUseCaseEdit?: (record: API.UsecaseItem) => void;
  onUseCaseView?: (record: API.UsecaseItem) => void;
  onUseCaseDelete?: (record: API.UsecaseItem) => void;
  columns?: ProColumns<API.UsecaseItem>[];
}

const AddModalForm: React.FC<AddModalFormProps> = ({
  modalOpen,
  onCancel,
  onSubmit,
  currentRow,
  selectedLlmType,
  setSelectedLlmType,
  requestHeaders,
  // setRequestHeaders,
  onUseCaseEdit,
  onUseCaseView,
  onUseCaseDelete,
  columns
}) => {
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [prompts, setPrompts] = useState<API.UsecaseItem[]>([]);
  const [currentTotal, setCurrentTotal] = useState<number>(0);
  const actionRef = useRef<ActionType>();
  const [selectedRowsState, setSelectedRows] = useState<API.UsecaseItem[]>([]); // 修改类型
  const [showData, setShowData] = useState<any>({});
  const [showStatistics, setShowStatistics] = useState<any>({});
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedUseCases, setSelectedUseCases] = useState<API.UsecaseItem[]>([]); // 第二个表格的数据
  // 添加表单引用
  const [form] = Form.useForm();
  // 添加状态来保存表单数据
  const [formData, setFormData] = useState<any>({});


  // 获取树形数据的函数
  const fetchTreeData = async (parent_id?: string) => {
    try {
      parent_id = parent_id || '';
      setLoading(true);

      const res = await getDictList({
        limit: 1000,
        offset: 1,
        keyword: '',
        parent_id: '',
      });

      if (res.status === 200 && res.data) {
        const transformedData = res.data.map(item => ({
          title: item.name || item.id,
          key: item.id,
          children: [],
          isLeaf: false,
          raw: item
        }));

        setTreeData(transformedData);
      }
    } catch (error) {
      console.error('获取树形数据失败:', error);
      message.error('获取树形数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理节点展开事件
  const onExpand = async (expandedKeys: React.Key[], { expanded, node }: any) => {
    setExpandedKeys(expandedKeys);

    if (expanded && node.children.length === 0) {
      await loadChildNodes(node.key);
    }
  };

  // 将子节点转换为树形结构
  const transformChildData = (nodes: any[]): TreeDataNode[] => {
    return nodes.map(node => ({
      title: node.name,
      key: node.id,
      children: node.children && node.children.length > 0
        ? transformChildData(node.children)
        : [],
      isLeaf: !node.children || node.children.length === 0,
      raw: node
    }));
  };

  // 加载子节点的函数
  const loadChildNodes = async (nodeId: string) => {
    try {
      setLoading(true);

      const res = await getDictTree(nodeId);

      if (res && res.status === 200 && res.data) {
        const childNodes = res.data;
        const transformedChildData = transformChildData(childNodes);

        setTreeData(prevTreeData => {
          const updateNodeChildren = (nodes: TreeDataNode[]): TreeDataNode[] => {
            return nodes.map(node => {
              if (node.key === nodeId) {
                return {
                  ...node,
                  children: transformedChildData,
                  isLeaf: transformedChildData.length === 0
                };
              }
              if (node.children) {
                return {
                  ...node,
                  children: updateNodeChildren(node.children)
                };
              }
              return node;
            });
          };
          return updateNodeChildren(prevTreeData);
        });
      }
    } catch (error) {
      console.error('加载子节点失败:', error);
      message.error('加载子节点失败');
    } finally {
      setLoading(false);
    }
  };

  // 递归查找匹配节点
  const findMatchNodes = (data: TreeDataNode[], keyword: string): string[] => {
    const result: string[] = [];
    data.forEach(item => {
      if (item.title.includes(keyword)) {
        result.push(item.key);
      }
      if (item.children) {
        result.push(...findMatchNodes(item.children, keyword));
      }
    });
    return result;
  };

  // Tree 节点选择时更新数据
  const onSelect = async (keys: string[]) => {
    setSelectedKeys(keys);

    if (keys.length > 0) {
      const selectedId = keys[0] as string;
      console.log('选中的节点ID:', selectedId);

      try {
        const [dictDetailResponse, useCaseResponse] = await Promise.all([
          getDictDetail(selectedId),
          getUseCaseList({
            limit: 0,
            offset: 1,
            classification_id: selectedId,
            keyword: searchKeyword || '',
          })
        ]);
      //  console.log('用例列表请求参数:', useCaseResponse);
       
        // 更新字典详情
        if (dictDetailResponse?.status === 200 && dictDetailResponse.data) {
          const dictData = dictDetailResponse.data;
          setShowData(dictData);
          console.log('字典详情:', dictData);
        }

        // 更新用例列表数据
        if (useCaseResponse?.status === 200) {

          setPrompts(useCaseResponse.data || []);
          setCurrentTotal(useCaseResponse.total || 0);
          // console.log('用例列表数据:', useCaseResponse.data);

          // 刷新表格
          actionRef.current?.reload();
        }

      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取数据失败');
      }
    } else {
      setShowData({});
    }
  };

  // 处理搜索事件
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    const matchKeys = findMatchNodes(treeData, value);
    if (matchKeys.length > 0) {
      setSelectedKeys([matchKeys[0]]);
      onSelect([matchKeys[0]]);
    } else {
      setSelectedKeys([]);
      setShowData({});
    }
  };

  // 处理表单提交
  const handleFinish = async (values: any) => {
    try {
      // 将选中的用例添加到提交数据中
      const submitData = {
        ...values,
        selectedUseCases: selectedUseCases, // 传递勾选的用例列表
      };
      const success = await onSubmit(submitData);
      if (success) {
        // 重置状态
        setSelectedKeys([]);
        setShowData({});
        setSearchKeyword('');
        onCancel();
      }
      return success;
    } catch (error) {
      console.error('提交失败:', error);
      return false;
    }
  };
  // 处理关闭按钮
  const handleClose = () => {
    try {
      // 重置所有状态
      setSelectedKeys([]);
      setSearchKeyword('');
      setPrompts([]);
      setCurrentTotal(0);
      setSelectedRows([]);
      setShowData({});
      setShowStatistics({});
      setSelectedUseCases([]);
      setExpandedKeys([]);

      // 关闭模态框
      onCancel();
    } catch (error) {
      console.error('关闭失败:', error);
      message.error('关闭失败');
    }
  };
  // 添加用例到第二个表格
  const handleAddUseCases = () => {
    if (selectedRowsState.length === 0) {
      message.warning('请先勾选要添加的用例');
      return;
    }

    // 过滤掉已经存在的用例，避免重复添加
    const newUseCases = selectedRowsState.filter(
      newItem => !selectedUseCases.some(existingItem => existingItem.id === newItem.id)
    );

    if (newUseCases.length === 0) {
      message.warning('选中的用例已存在');
      return;
    }

    // 添加到第二个表格
    setSelectedUseCases([...selectedUseCases, ...newUseCases]);
    // console.log('添加的用例:', selectedUseCases);

    // 清空勾选状态
    setSelectedRows([]);

    message.success(`成功添加 ${newUseCases.length} 个用例`);
  };

  // 从第二个表格删除用例
  const handleRemoveUseCase = (record: API.UsecaseItem) => {
    const newSelectedUseCases = selectedUseCases.filter(item => item.id !== record.id);
    setSelectedUseCases(newSelectedUseCases);
    message.success('删除成功');
  };
  // 处理第一个表格的搜索
  const handleTableSearch = (value: string) => {
    // 更新搜索关键词
    setSearchKeyword(value);

    // 如果有选中的节点，重新获取数据
    if (selectedKeys.length > 0) {
      const selectedId = selectedKeys[0] as string;

      // 重新调用API获取过滤后的数据
      // console.log('搜索数组:',selectedKeys);
      // console.log('搜索关键词:', value);

      getUseCaseList({
        limit: 10,
        offset: 1,
        classification_id: selectedId,
        keyword: value || '',
      }).then(res => {
        if (res.status === 200) {
          setPrompts(res.data || []);
          setCurrentTotal(res.total || 0);
          // 刷新表格
          actionRef.current?.reload();
          console.log('搜索结果:', res.data);
        }
      }).catch(error => {
        console.error('搜索失败:', error);
        message.error('搜索失败');
      });
    }
  };

  // 当模态框打开时获取数据并初始化编辑状态
  useEffect(() => {
    if (modalOpen) {
      fetchTreeData();

      // 如果是编辑模式，初始化数据
      if (currentRow?.id) {
        initializeEditData(); // 现在是异步函数
      } else {
        // 新建模式，重置所有状态
        resetAllStates();
      }
    }
  }, [modalOpen, currentRow?.id]);
  // 初始化编辑数据的函数
  const initializeEditData = async () => {
    try {
      console.log('开始初始化编辑数据:', currentRow);

      // 初始化表单数据
      const editFormData = {
        name: currentRow?.name || '',
        description: currentRow?.description || '',
        tags: currentRow?.tags || [],
        ...currentRow
      };

      // 设置表单数据到状态
      setFormData(editFormData);

      // 延迟设置表单值，确保组件已渲染
      setTimeout(() => {
        form.setFieldsValue(editFormData);
        console.log('表单数据已设置:', editFormData);
      }, 200);

      // 通过策略ID获取已选用例
      if (currentRow?.id) {
        await loadSelectedUseCases(currentRow.id);
      }

      console.log('编辑数据初始化完成');
    } catch (error) {
      console.error('初始化编辑数据失败:', error);
      message.error('初始化编辑数据失败');
    }
  };

  // 根据策略ID加载已选用例
  const loadSelectedUseCases = async (strategyId: string) => {
    try {
      console.log('开始加载已选用例，策略ID:', strategyId);

      const response = await getUseCaseList({
        limit: 0,
        offset: 0,
        keyword: "",
        classification_id: "",
        strategy_id: strategyId
      });

      if (response?.status === 200 && response.data) {
        setSelectedUseCases(response.data);
        console.log('已选用例加载成功:', response.data);
        console.log('已选用例数量:', response.data.length);
      } else {
        console.log('未获取到已选用例数据');
        setSelectedUseCases([]);
      }
    } catch (error) {
      console.error('加载已选用例失败:', error);
      message.error('加载已选用例失败');
      setSelectedUseCases([]);
    }
  };
  // 重置所有状态的函数
  const resetAllStates = () => {
    console.log('重置所有状态');
    setFormData({});
    setSelectedKeys([]);
    setSearchKeyword('');
    setPrompts([]);
    setCurrentTotal(0);
    setSelectedRows([]);
    setShowData({});
    setShowStatistics({});
    setSelectedUseCases([]);
    setExpandedKeys([]);
    setCurrent(0);
    form.resetFields();
  };

  const [current, setCurrent] = useState(0);
  // 在切换步骤时保存表单数据
  const onChange = async (value: number) => {
    try {
      // 在切换步骤前保存当前表单数据
      const currentFormValues = form.getFieldsValue();
      setFormData(prev => ({ ...prev, ...currentFormValues }));

      // console.log('切换到步骤:', value);
      // console.log('当前表单数据:', currentFormValues);
      setCurrent(value);
    } catch (error) {
      console.error('步骤切换失败:', error);
    }
  };

  return (
    <ModalForm
      title={currentRow?.id ? "编辑扫描策略" : "创建扫描策略"}
      width={current === 0 ? 600 : 1840}
      form={form} // 添加表单引用
      open={modalOpen}
      onOpenChange={(open) => {
        if (!open) {
          onCancel();
        }
      }}
      modalProps={{
        destroyOnClose: true,
        bodyStyle: { padding: 0 },
      }}
      onFinish={handleFinish}
      submitter={{
        searchConfig: {
          submitText: '保存',
          resetText: '关闭',
        },
        render: (props, doms) => {
          return (
            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
              {/* 左侧：步骤导航按钮 */}
              <div style={{ display: 'flex', gap: '8px' }}>
                {/* 第一步：只显示下一步 */}
                {current === 0 && (
                  <Button
                    type="primary"
                    onClick={() => {
                      // 验证第一步表单
                      form.validateFields(['name', 'description']).then((values) => {
                        // 保存表单数据到状态
                        setFormData(prev => ({ ...prev, ...values }));
                        setCurrent(1);
                      }).catch((error) => {
                        console.log('第一步验证失败:', error);
                      });
                    }}
                  >
                    下一步
                  </Button>
                )}
      
                {/* 第二步：显示上一步和下一步 */}
                {current === 1 && (
                  <>
                    <Button
                      onClick={() => setCurrent(0)}
                    >
                      上一步
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        if (selectedUseCases.length === 0) {
                          message.error('请至少选择一个用例');
                          return;
                        }
                        setCurrent(2);
                      }}
                    >
                      下一步
                    </Button>
                  </>
                )}
      
                {/* 第三步：只显示上一步 */}
                {current === 2 && (
                  <Button
                    onClick={() => setCurrent(1)}
                  >
                    上一步
                  </Button>
                )}
              </div>
      
              {/* 右侧：最后一步才显示保存和取消按钮 */}
              {current === 2 && (
                <div style={{ display: 'flex', gap: '8px' }}>
                  <Button
                    onClick={() => {
                      // 重置所有状态
                      resetAllStates();
                      // 执行关闭逻辑
                      onCancel();
                    }}
                  >
                    取消
                  </Button>
                  <Button
                    type="primary"
                    onClick={async () => {
                      try {
                        // 获取最新的表单数据
                        const currentFormValues = form.getFieldsValue();
                        const allFormData = { ...formData, ...currentFormValues };
      
                        if (!allFormData.name) {
                          message.error('请填写策略名称');
                          setCurrent(0);
                          return;
                        }
      
                        if (!allFormData.description) {
                          message.error('请填写策略描述');
                          setCurrent(0);
                          return;
                        }
      
                        if (selectedUseCases.length === 0) {
                          message.error('请至少选择一个用例');
                          setCurrent(1);
                          return;
                        }
      
                        // 构建提交数据
                        const submitData = {
                          // 如果是编辑模式，包含ID
                          ...(currentRow?.id && { id: currentRow.id }),
                          
                          // 第一步：基本配置
                          name: allFormData.name,
                          description: allFormData.description,
                          tags: allFormData.tags || [],
      
                          // 第二步：已选用例
                          selectedUseCases: selectedUseCases,
                          useCaseCount: selectedUseCases.length,
                          useCaseIds: selectedUseCases.map(item => item.id),
      
                          // 包含所有表单字段
                          ...allFormData,
                        };
      
                        console.log('提交数据:', submitData);
      
                        // 调用提交方法
                        const success = await onSubmit(submitData);
      
                        if (success) {
                          // 提交成功后重置所有状态
                          resetAllStates();
                          // 关闭模态框
                          onCancel();
                          message.success(currentRow?.id ? '策略更新成功' : '策略创建成功');
                        }
      
                      } catch (error) {
                        console.error('提交失败:', error);
                        message.error('提交失败，请重试');
                      }
                    }}
                  >
                    保存
                  </Button>
                </div>
              )}
            </div>
          );
        },
      }}
    >
      {/* <Layout style={{ minHeight: '500px' }}> */}
      <Steps
        current={current}
        onChange={onChange}
        items={[
          {
            title: 'Step 1',
            description: '基本配置',
          },
          {
            title: 'Step 2',
            description: '用例选择',
          },
          {
            title: 'Step 3',
            description: '策略配置',
          },
        ]}
      />

      {/* 第一步：基本配置 */}
      {current === 0 && (
        <div style={{ padding: '24px' }}>
          <h3>基本配置</h3>
          <ProFormText
            name="name"
            label="策略名称"
            width={'md'}
            placeholder="请输入策略名称"
            rules={[{ required: true, message: '策略名称不能为空' }]}
          />
          <ProFormSelect
            name="tags"
            label="策略标签"
            mode="tags"
            placeholder="请选择或输入标签"
            fieldProps={{
              style: { width: '100%' },
              tokenSeparators: [',', ' '],
              maxTagCount: 'responsive',
              maxTagTextLength: 20,
            }}
            options={[
              { label: 'TC260-003', value: 'TC260-003' },
              { label: '合规', value: '合规' },
              { label: '提示词注入', value: '提示词注入' },
              { label: '敏感词', value: '敏感词' },
              { label: 'OWASP Top 10', value: 'OWASP_Top_10' },
              { label: 'WDTA', value: 'WDTA' }
            ]}
            help="可选择预设标签或输入自定义标签，支持多选"
          />
          <ProFormTextArea
            name="description"
            label="策略描述"
            placeholder="请输入策略描述"
            rules={[{ required: true, message: '策略描述不能为空' }]}
          />
        </div>
      )}

      {/* 第二步：用例选择 */}
      {current === 1 && (
        <Layout style={{ minHeight: '700px' }}>
          <Sider style={{ background: 'transparent' }} width={350}>
            <Tabs>
              <Tabs.TabPane
                tab={<span><ProjectOutlined />字典管理</span>}
                key="dataset"
              />
            </Tabs>
            <Input.Search
              placeholder="输入搜索关键字"
              style={{ marginBottom: 16 }}
              onSearch={handleSearch}
            />
            <Tree
              showLine
              treeData={treeData}
              height={700}
              selectedKeys={selectedKeys}
              expandedKeys={expandedKeys}
              onSelect={onSelect}
              onExpand={onExpand}
              loading={loading}
            />
          </Sider>

          <Content style={{ minHeight: 280, paddingLeft: 10 }}>
            <Row>
              <Col span={12}>
                <ProTable<API.UsecaseItem, API.PageParams>
                  headerTitle={`勾选列表`}
                  toolbar={{
                    search: {
                      onSearch: handleTableSearch,
                    },
                  }}
                  search={false}
                  columns={columns || []}
                  rowKey="id"
                  actionRef={actionRef}
                  dataSource={prompts}
                  size="small"
                  toolBarRender={() => [
                    <Button
                      type="primary"
                      key="addAll"
                      onClick={() => {
                        setSelectedUseCases([...selectedUseCases, ...prompts]);
                        message.success(`成功添加 ${prompts.length} 个用例`);
                      }}
                    >
                      <PlusOutlined />添加全部用例
                    </Button>,
                    <Button
                      type="primary"
                      key="addSelected"
                      onClick={handleAddUseCases}
                      disabled={selectedRowsState.length === 0}
                    >
                      <PlusOutlined />添加用例 ({selectedRowsState.length})
                    </Button>,
                  ]}
                  rowSelection={{
                    onChange: (_, selectedRows) => {
                      setSelectedRows(selectedRows);
                    },
                  }}
                  pagination={{
                    pageSize: 15,
                    total: currentTotal || 0,
                    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
                  }}
                />
              </Col>
              <Col span={12} style={{ paddingLeft: 10 }}>
                <ProTable<API.UsecaseItem, API.PageParams>
                  headerTitle={`已选用例列表 (${selectedUseCases.length})`}
                  style={{ minHeight: '400px' }}
                  columns={[
                    ...(columns || []).filter(col => col.dataIndex !== 'option' && col.valueType !== 'option'),
                    {
                      title: '操作',
                      dataIndex: 'option',
                      valueType: 'option',
                      width: '10%',
                      render: (_, record) => [
                        <Button
                          key="delete"
                          type="link"
                          danger
                          onClick={() => handleRemoveUseCase(record)}
                        >
                          删除
                        </Button>,
                      ],
                    },
                  ]}
                  rowKey="id"
                  dataSource={selectedUseCases}
                  size="small"
                  search={false}
                  pagination={{
                    pageSize: 15,
                    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
                  }}
                  toolBarRender={() => [
                    <Button
                      key="clear"
                      onClick={() => {
                        setSelectedUseCases([]);
                        setSelectedRows([]);
                        actionRef.current?.clearSelected?.();
                        message.success('已清空所有用例');
                      }}
                      disabled={selectedUseCases.length === 0}
                    >
                      清空所有
                    </Button>,
                  ]}
                />
              </Col>
            </Row>
          </Content>
        </Layout>
      )}

      {/* 第三步：策略配置 */}
      {current === 2 && (
        <div style={{ padding: '24px' }}>
          <Row>
            <Col span={8}>
              {/* 第一步内容回顾 */}
              <Card title="基本配置" style={{ marginBottom: 16, minHeight: '545px' }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <p><strong>策略名称：</strong>
                      {currentRow?.name || formData.name || form?.getFieldValue('name') || '未填写'}
                    </p>
                  </Col>
                  <Col span={12}>
                    <p><strong>策略描述：</strong>
                      {currentRow?.description || formData.description || form?.getFieldValue('description') || '未填写'}
                    </p>
                  </Col>
                </Row>
                <Row gutter={16}>
                  <Col span={24}>
                    <p><strong>策略标签：</strong>
                      {(() => {
                        const tags = currentRow?.tags || formData.tags || form?.getFieldValue('tags') || [];
                        return tags.length > 0 ? (
                          <div style={{ marginTop: 8 }}>
                            {tags.map((tag: string, index: number) => (
                              <Tag key={index} color="blue" style={{ marginBottom: 4 }}>
                                {tag}
                              </Tag>
                            ))}
                          </div>
                        ) : (
                          <span style={{ color: '#999', fontStyle: 'italic' }}>未设置标签</span>
                        );
                      })()}
                    </p>
                  </Col>
                </Row>
              </Card>
            </Col>
            <Col span={15} offset={1}>
              {/* 第二步内容回顾 */}
              <Card title="已选用例" style={{ marginBottom: 16, minHeight: '545px' }}>
                <p><strong>已选用例数量：</strong>{selectedUseCases.length} 个</p>
                {selectedUseCases.length > 0 && (
                  <div style={{ maxHeight: '400px' }}>
                    <ProTable<API.UsecaseItem, API.PageParams>
                      columns={[
                        {
                          title: '序号',
                          dataIndex: 'id',
                          valueType: 'indexBorder',
                          width: 60,
                          hideInSearch: true,
                        },
                        {
                          title: 'Prompt',
                          dataIndex: 'payload',
                          ellipsis: true,
                          search: false,
                          width: '50%'
                        },
                        {
                          title: '数据集',
                          dataIndex: 'classification',
                          search: false,
                          ellipsis: true,
                          render: (text, record) => {
                            if (record.classification && record.classification.path && Array.isArray(record.classification.path)) {
                              return record.classification.path.map(item => item.name).join(' > ') + ' > ' + record.classification.name;
                            }
                            return '-';
                          },
                        },
                      ]}
                      dataSource={selectedUseCases}
                      rowKey="id"
                      pagination={{
                        pageSize: 10,
                        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
                      }}
                      search={false}
                      toolBarRender={false}
                      size="small"
                    />
                  </div>
                )}
                {selectedUseCases.length === 0 && (
                  <p style={{ color: '#999', fontStyle: 'italic' }}>未选择任何用例</p>
                )}
              </Card>
            </Col>
          </Row>
        </div>
      )}
    </ModalForm>
  );
};

export default AddModalForm;