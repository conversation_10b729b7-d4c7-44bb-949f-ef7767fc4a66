## ✨ 简介

使用`React18`,`Typescript`,`Vite`,`Antd5.0`，`Vite`实现自动生成路由，支持`KeepAlive`功能，`react-redux`状态管理，支持虚拟滚动表格。
适配UI5.0风格，一键切换暗黑，浅色模式，可扩展。

## ⭐ 项目演示

[演示地址: http://***********:8010/#/login](http://***********:8010/#/login)<br>
![image](%E6%B5%85%E8%89%B2.png)
![image](%E6%9A%97%E9%BB%91.png)

## ⭐ 安装使用

- 获取项目代码

```bash
git clone https://gitlab.inone.nsfocus.com/ucd-ui/nsfocus-ui5.0-frame/-/tree/main/UI5.0-React%2Bantd5.x
```

- 选择目录

```bash
cd ui5.0-react18-antd5
```

- 安装全局依赖依赖，存在则不用安装

```bash
npm i -g pnpm
```

- 安装依赖

```bash
pnpm install
```

##### 如果使用pnpm安装依赖出现安装失败问题，请使用梯子或yarn安装

- 运行

```bash
pnpm dev
```

- 打包

```bash
pnpm build
```

## ⭐ 已完成

- [x] 主题换肤功能
- [x] 密码强度显示
- [x] KeepAlive功能
- [x] 表格虚拟滚动优化
- [x] form添加富文本、自定义渲染
- [x] 新增跳转单独页逻辑
- [x] 多页签，快捷关闭等批量操作
- [x] 局部全屏，全屏功能

## ⭐ 关于共建 && Git 提交示例

### 发现一个BUG，并解决，积分2分；提供一个优秀功能或公共组件封装，积5分；框架搭建15分

### Git提交不规范会导致无法提交，`feat`关键字可以按照下面`Git 贡献提交规范`来替换。提交至‘dev’分支

```
git add .
git commit -m "feat: 新增功能"
git push
```

## ⭐ 路由

路由根据文件夹路径自动生成，路径包含以下文件名或文件夹名称则不生成：

- components
- utils
- lib
- hooks
- tests
- __test__
- model.tsx
- [...all].tsx

可自行在 src/router/utils/config.ts 修改路由生成规则。

## ⭐ 关于封装

  1. 功能扩展，在原有的api上拓展。
  2. 功能整合，合并两个或两个以上组件的api。
  3. 样式统一，避免后期样式变动，导致牵一发而动全身，具体见 src/assets/css样式文件中。
  4. 公共组件二次封装或常用组件使用__Basic__开头，便于区分。
