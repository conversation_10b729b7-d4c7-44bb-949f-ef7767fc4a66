import React, { useState, useRef, useEffect } from 'react';
import { Form, Input, Tag, message, Tree, Layout, Button, Steps, Row, Col } from 'antd';
import { PlusOutlined, ProjectOutlined } from '@ant-design/icons';
import { ModalForm, ProFormText, ProFormTextArea, ProTable } from '@ant-design/pro-components';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import { getWordList, getWordCategoryList, addWordToLexicon } from '@/servers/configuration';

const { Content, Sider } = Layout;

interface TreeDataNode {
  title: string;
  key: string;
  children?: TreeDataNode[];
  isLeaf?: boolean;
  rule_class?: string;
  tag?: string;
}

interface AddModalFormProps {
  modalOpen: boolean;
  onCancel: () => void;
  onSubmit: (values: any) => Promise<boolean | any>;
  currentRow?: API.LexiconItem;
}

const AddModalForm: React.FC<AddModalFormProps> = ({
  modalOpen,
  onCancel,
  onSubmit,
  currentRow,
}) => {
  const [tags, setTags] = useState<string[]>(currentRow?.tag || []);
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [current, setCurrent] = useState(0);
  const [form] = Form.useForm();
  const [formData, setFormData] = useState<any>({});
  const [selectedWords, setSelectedWords] = useState<API.WordItem[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [selectedRuleClass, setSelectedRuleClass] = useState<string>('');
  const [selectedTag, setSelectedTag] = useState<string>('');
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]);
  const actionRef = useRef<ActionType>();
  const lexiconWordsActionRef = useRef<ActionType>(); // 词库词条表格的引用

  // 重置所有状态
  const resetAllStates = () => {
    setTags(currentRow?.tag || []);
    setInputVisible(false);
    setInputValue('');
    setCurrent(0);
    setFormData({});
    setSelectedWords([]);
    setSelectedKeys([]);
    setSelectedRuleClass('');
    setSelectedTag('');
    setSearchKeyword('');
    setTreeData([]);
    form.resetFields();
  };

  // 获取词条分类数据，转换为树形结构
  const fetchCategoryData = async () => {
    try {
      const res = await getWordCategoryList();

      if (res && res.status === 200 && res.data) {
        // 转换为简单的树形结构
        const transformedData = res.data.map(item => ({
          title: item.rule_class,
          key: item.rule_class,
          rule_class: item.rule_class,
          children: item.tag.map(tag => ({
            title: tag,
            key: `${item.rule_class}-${tag}`,
            rule_class: item.rule_class,
            tag: tag,
            isLeaf: true,
          })),
        }));
        setTreeData(transformedData);
      }
    } catch (error) {
      console.error('获取词条分类失败:', error);
      message.error('获取词条分类失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    if (modalOpen && current === 1) {
      fetchCategoryData();
    }
  }, [modalOpen, current]);

  // 当currentRow变化时，更新tags状态
  useEffect(() => {
    if (currentRow?.tag) {
      setTags(currentRow.tag);
    } else {
      setTags([]);
    }
  }, [currentRow]);

  // 词条表格列定义
  const wordColumns: ProColumns<API.WordItem>[] = [
    {
      title: '词条',
      dataIndex: 'word',
      key: 'word',
      ellipsis: true,
    },
    {
      title: '规则分类',
      dataIndex: 'rule_class',
      key: 'rule_class',
      ellipsis: true,
    },
    {
      title: '标签',
      dataIndex: 'tag',
      key: 'tag',
      ellipsis: true,
    },
  ];

  // 词库词条表格列定义
  const lexiconWordColumns: ProColumns<API.WordItem>[] = [
    {
      title: '词条',
      dataIndex: 'word',
      key: 'word',
      ellipsis: true,
    },
    {
      title: '规则分类',
      dataIndex: 'rule_class',
      key: 'rule_class',
      ellipsis: true,
    },
    {
      title: '标签',
      dataIndex: 'tag',
      key: 'tag',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: () => (
        <Button
          type="link"
          size="small"
          danger
          onClick={async () => {
            try {
              // 这里需要调用删除词库词条的API
              // 暂时先提示，等后端API确定
              message.info('删除功能待后端API确定');
              
              // 删除成功后刷新列表
              // if (lexiconWordsActionRef.current) {
              //   lexiconWordsActionRef.current.reload();
              // }
            } catch (error) {
              console.error('删除词条失败:', error);
              message.error('删除词条失败');
            }
          }}
        >
          删除
        </Button>
      ),
    },
  ];

  // 处理树节点选择
  const handleTreeSelect = (keys: React.Key[], info: any) => {
    setSelectedKeys(keys);
    
    if (keys.length > 0) {
      const selectedNode = info.node;
      if (selectedNode) {
        setSelectedRuleClass(selectedNode.rule_class || '');
        setSelectedTag(selectedNode.tag || '');
      }
    } else {
      setSelectedRuleClass('');
      setSelectedTag('');
    }
    
    if (actionRef.current) {
      actionRef.current.reload();
    }
  };

  // 处理词条搜索
  const handleWordSearch = (value: string) => {
    setSearchKeyword(value);
    if (actionRef.current) {
      actionRef.current.reload();
    }
  };

  // 添加全部词条处理函数
  const handleAddAllWords = async () => {
    if (!selectedRuleClass) {
      message.warning('请先选择词条分类');
      return;
    }

    if (!currentRow?.id) {
      message.warning('请先保存词库，再添加词条');
      return;
    }

    try {
        const response = await addWordToLexicon({
            lexicon_id: String(currentRow.id),
            rule_class: selectedRuleClass,
            tag: selectedTag,
        });
        console.log('添加全部词条响应:', response);
        
        if (response && response.status === 200) {
          message.success(`成功添加${selectedTag ? `"${selectedTag}"标签` : `"${selectedRuleClass}"分类`}下的所有词条`);
          // 刷新词库词条列表
          if (lexiconWordsActionRef.current) {
            lexiconWordsActionRef.current.reload();
          }
        } else {
          message.error('添加词条失败');
        }
    } catch (error) {
      console.error('添加全部词条失败:', error);
      message.error('添加全部词条失败');
    }
  };

  // 添加选择的词条处理函数
  const handleAddSelectedWords = async () => {
    if (selectedWords.length === 0) {
      message.warning('请先选择要添加的词条');
      return;
    }

    if (!currentRow?.id) {
      message.warning('请先保存词库，再添加词条');
      return;
    }

    try {
      const response = await addWordToLexicon({
        lexicon_id: String(currentRow.id),
        word_id_list: selectedWords.map(item => String(item.id)),
      });
      console.log('添加选择词条响应:', response);
      
      if (response && response.status === 200) {
        message.success(`成功添加${selectedWords.length}个词条到词库`);
        // 清空选择
        setSelectedWords([]);
        // 刷新词库词条列表
        if (lexiconWordsActionRef.current) {
          lexiconWordsActionRef.current.reload();
        }
      } else {
        message.error('添加词条失败');
      }
    } catch (error) {
      console.error('添加选择词条失败:', error);
      message.error('添加选择词条失败');
    }
  };

  // 处理添加标签
  const handleClose = (removedTag: string) => {
    const newTags = tags.filter((tag) => tag !== removedTag);
    setTags(newTags);
  };

  const showInput = () => {
    setInputVisible(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputConfirm = () => {
    if (inputValue && tags.indexOf(inputValue) === -1) {
      setTags([...tags, inputValue]);
    }
    setInputVisible(false);
    setInputValue('');
  };

  return (
    <ModalForm
      title={currentRow ? '编辑词库' : '创建词库'}
      width={current === 0 ? 600 : 1840}
      form={form}
      open={modalOpen}
      onOpenChange={(visible) => {
        if (!visible) {
          resetAllStates();
          onCancel();
        }
      }}
      modalProps={{
        destroyOnClose: true,
        bodyStyle: { padding: 0 },
      }}
      initialValues={{
        name: currentRow?.name || '',
        description: currentRow?.description || '',
      }}
      submitter={{
        searchConfig: {
          submitText: '保存',
          resetText: '关闭',
        },
        render: () => {
          return (
            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
              {/* 左侧：步骤导航按钮 */}
              <div style={{ display: 'flex', gap: '8px' }}>
                {/* 只有编辑模式才显示下一步按钮 */}
                {currentRow?.id && current === 0 && (
                  <Button
                    type="primary"
                    onClick={() => {
                      // 验证第一步表单
                      form.validateFields(['name']).then((values) => {
                        // 保存表单数据到状态
                        setFormData((prev: any) => ({ ...prev, ...values, tag: tags }));
                        setCurrent(1);
                      }).catch((error) => {
                        console.log('第一步验证失败:', error);
                      });
                    }}
                  >
                    下一步
                  </Button>
                )}

                {/* 第二步：显示上一步 */}
                {current === 1 && (
                  <Button
                    onClick={() => setCurrent(0)}
                  >
                    上一步
                  </Button>
                )}
              </div>

              {/* 右侧：保存和取消按钮 */}
              <div style={{ display: 'flex', gap: '8px' }}>
                <Button
                  onClick={() => {
                    resetAllStates();
                    onCancel();
                  }}
                >
                  取消
                </Button>
                <Button
                  type="primary"
                  onClick={async () => {
                    try {
                      // 获取最新的表单数据
                      const currentFormValues = form.getFieldsValue();
                      const allFormData = { ...formData, ...currentFormValues };

                      if (!allFormData.name) {
                        message.error('请填写词库名称');
                        setCurrent(0);
                        return;
                      }

                      // 构建提交数据
                      const submitData = {
                        ...(currentRow?.id && { id: currentRow.id }),
                        name: allFormData.name,
                        description: allFormData.description,
                        tag: tags,
                        selectedWords: selectedWords,
                        wordCount: selectedWords.length,
                        wordIds: selectedWords.map(item => item.id),
                        ...allFormData,
                      };

                      console.log('提交数据:', submitData);

                      // 调用提交方法
                      const result = await onSubmit(submitData);

                      if (result) {
                        resetAllStates();
                        onCancel();
                        message.success(currentRow?.id ? '词库更新成功' : '词库创建成功');
                      }
                    } catch (error) {
                      console.error('操作失败:', error);
                      message.error('操作失败，请重试');
                    }
                  }}
                >
                  保存
                </Button>
              </div>
            </div>
          );
        },
      }}
    >
      <Steps
        current={current}
        onChange={setCurrent}
        items={[
          {
            title: 'Step 1',
            description: '基本配置',
          },
          {
            title: 'Step 2',
            description: '词条选择',
          },
        ]}
      />

      {/* 第一步：基本配置 */}
      {current === 0 && (
        <div style={{ padding: '24px' }}>
          <h3>基本配置</h3>
          <ProFormText
            name="name"
            label="词库名称"
            placeholder="请输入词库名称"
            rules={[
              {
                required: true,
                message: '请输入词库名称',
              },
            ]}
          />

          <ProFormTextArea
            name="description"
            label="描述"
            placeholder="请输入词库描述"
            fieldProps={{
              rows: 4,
            }}
          />

          <Form.Item label="标签">
            <div>
              {tags.map((tag) => (
                <Tag
                  key={tag}
                  closable
                  onClose={() => handleClose(tag)}
                  style={{ marginBottom: 8 }}
                >
                  {tag}
                </Tag>
              ))}
              {inputVisible && (
                <Input
                  type="text"
                  size="small"
                  style={{ width: 78 }}
                  value={inputValue}
                  onChange={handleInputChange}
                  onBlur={handleInputConfirm}
                  onPressEnter={handleInputConfirm}
                  autoFocus
                />
              )}
              {!inputVisible && (
                <Tag
                  onClick={showInput}
                  style={{
                    background: '#fff',
                    borderStyle: 'dashed',
                    cursor: 'pointer',
                  }}
                >
                  <PlusOutlined /> 添加标签
                </Tag>
              )}
            </div>
          </Form.Item>
        </div>
      )}

      {/* 第二步：词条选择 - 只在编辑模式下显示 */}
      {current === 1 && currentRow?.id && (
        <Layout style={{ minHeight: '700px' }}>
          <Sider style={{ background: 'transparent' }} width={350}>
            <div style={{ padding: '16px' }}>
              <h4><ProjectOutlined /> 词条分类</h4>
              <Input.Search
                placeholder="搜索分类"
                style={{ marginBottom: 16 }}
                onSearch={handleWordSearch}
              />
              <Tree
                showLine
                treeData={treeData}
                height={600}
                selectedKeys={selectedKeys}
                onSelect={handleTreeSelect}
              />
            </div>
          </Sider>
          <Content style={{ padding: '16px' }}>
            <Row gutter={16} style={{ height: '100%' }}>
              {/* 左侧：词条列表 */}
              <Col span={12}>
                <h4>词条列表</h4>
                <ProTable<API.WordItem>
                  actionRef={actionRef}
                  rowKey="id"
                  search={false}
                    request={async (params) => {
                    try {                        
                        const response = await getWordList({
                        limit: params.pageSize || 20,
                        offset: params.current || 1,
                        keyword: searchKeyword,
                        rule_class: selectedRuleClass,
                        tag: selectedTag,
                        });
                        
                        return {
                        data: response.data || [],
                        success: response.status === 200,
                        total: response.total || 0,
                        };
                    } catch (error) {
                        console.error('获取词条列表失败:', error);
                        return {
                        data: [],
                        success: false,
                        total: 0,
                        };
                    }
                    }}
                  columns={wordColumns}
                  rowSelection={{
                    type: 'checkbox',
                    selectedRowKeys: selectedWords.map(item => item.id).filter(Boolean) as React.Key[],
                    onSelect: (record, selected) => {
                      if (selected) {
                        setSelectedWords(prev => [...prev, record]);
                      } else {
                        setSelectedWords(prev => prev.filter(item => item.id !== record.id));
                      }
                    },
                    onSelectAll: (selected, selectedRows, changeRows) => {
                      if (selected) {
                        setSelectedWords(prev => [...prev, ...changeRows]);
                      } else {
                        const changeIds = changeRows.map(item => item.id);
                        setSelectedWords(prev => prev.filter(item => !changeIds.includes(item.id)));
                      }
                    },
                  }}
                  pagination={{
                    defaultPageSize: 20,
                    showSizeChanger: true,
                  }}
                  size="small"
                  toolBarRender={() => [
                    <Button
                      key="addSelected"
                      type="primary"
                      onClick={handleAddSelectedWords}
                      disabled={selectedWords.length === 0}
                    >
                      <PlusOutlined />添加选择的词条 ({selectedWords.length})
                    </Button>,
                    <Button
                      key="addAll"
                      type="primary"
                      onClick={handleAddAllWords}
                      disabled={selectedKeys.length === 0}
                    >
                      <PlusOutlined />添加全部词条
                    </Button>,
                  ]}
                />
              </Col>

              {/* 右侧：词库词条 */}
              <Col span={12}>
                <h4>词库词条 {currentRow?.id ? `(词库ID: ${currentRow.id})` : '(请先保存词库)'}</h4>
                <ProTable<API.WordItem>
                  actionRef={lexiconWordsActionRef}
                  rowKey="id"
                  search={false}
                  request={async (params) => {
                    // 只有编辑模式且有词库ID时才请求数据
                    if (!currentRow?.id) {
                      return {
                        data: [],
                        success: true,
                        total: 0,
                      };
                    }

                    try {
                      const response = await getWordList({
                        limit: params.pageSize || 20,
                        offset: params.current || 1,
                        keyword: '',
                        lexicon_id: String(currentRow.id),
                      });
                      
                      return {
                        data: response.data || [],
                        success: response.status === 200,
                        total: response.total || 0,
                      };
                    } catch (error) {
                      console.error('获取词库词条失败:', error);
                      return {
                        data: [],
                        success: false,
                        total: 0,
                      };
                    }
                  }}
                  columns={lexiconWordColumns}
                  pagination={{
                    defaultPageSize: 20,
                    showSizeChanger: true,
                  }}
                  size="small"
                  toolBarRender={() => [
                    <Button
                      key="refresh"
                      onClick={() => {
                        if (lexiconWordsActionRef.current) {
                          lexiconWordsActionRef.current.reload();
                        }
                      }}
                    >
                      刷新
                    </Button>,
                  ]}
                />
              </Col>
            </Row>
          </Content>
        </Layout>
      )}
    </ModalForm>
  );
};

export default AddModalForm;
