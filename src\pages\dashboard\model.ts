import type { FormList } from "#/form";
import type { TFunction } from "i18next";

// 搜索数据
export const searchList = (t: TFunction): FormList[] => [
  {
    label: t('public.date'),
    name: 'pay_date',
    component: 'RangePicker',
    componentProps: {
      allowClear: false,
    }
  },
  {
    label: t('dashboard.gameID'),
    name: 'game_ids',
    wrapperCol: 200,
    component: 'GameSelect',
  },
  {
    label: t('dashboard.cooperativeCompany'),
    name: 'partners',
    wrapperCol: 200,
    component: 'PartnerSelect'
  },
  {
    label: t('dashboard.fullServerRecharge'),
    name: 'all_pay',
    wrapperCol: 15,
    component: 'Checkbox'
  }
];

export const tableData = [
  {
    key: "1",
    name: "这是事件名称",
    age: 32,
    address: "New York No. 1 Lake Park",
    tags: ["nice", "developer"],
    status: "危",
    content: "这里是内容文字",
    config: "radius",
    time: "2022-11-11 12:30",
  },
  {
    key: "2",
    name: "这是事件名称",
    age: 42,
    address: "London No. 1 Lake Park",
    tags: ["loser"],
    status: "高",
    content: "这里是内容文字",
    config: "input",
    time: "2022-11-10 12:30",
  },
  {
    key: "3",
    name: "这是事件名称",
    age: 32,
    address: "Sidney No. 1 Lake Park",
    tags: ["cool", "teacher"],
    status: "中",
    content: "这里是内容文字",
    config: "radius",
    time: "2022-11-08 12:30",
  },
  {
    key: "4",
    name: "这是事件名称",
    age: 32,
    address: "Sidney No. 1 Lake Park",
    tags: ["cool", "teacher"],
    status: "低",
    content: "这里是内容文字",
    config: "input",
    time: "2022-11-01 12:30",
  },
  {
    key: "5",
    name: "这是事件名称",
    age: 32,
    address: "Sidney No. 1 Lake Park",
    tags: ["cool", "teacher"],
    status: "安",
    content: "这里是内容文字",
    config: "input",
    time: "2022-10-11 12:30",
  },
  {
    key: "6",
    name: "这是事件名称",
    age: 32,
    address: "Sidney No. 1 Lake Park",
    tags: ["cool", "teacher"],
    status: "安",
    content: "这里是内容文字",
    config: "input",
    time: "2022-10-11 12:30",
  },
];