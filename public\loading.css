.ma-mskLoading {
  width: 100%;
  height: 100vh;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
}

.ma-line-scale > div {
  background-color: #607d8b;
  width: 4px;
  height: 35px;
  border-radius: 2px;
  margin: 2px;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  display: inline-block;
}

.ma-line-scale > div:first-child {
  -webkit-animation: line-scale-data 1s cubic-bezier(0.2, 0.68, 0.18, 1.08) -0.4s infinite;
  animation: line-scale-data 1s cubic-bezier(0.2, 0.68, 0.18, 1.08) -0.4s infinite;
}

.ma-line-scale > div:nth-child(2) {
  -webkit-animation:line-scale-data 1s cubic-bezier(.2,.68,.18,1.08) -.3s infinite;
  animation: line-scale-data 1s cubic-bezier(0.2, 0.68, 0.18, 1.08) -0.3s infinite;
}

.ma-line-scale > div:nth-child(3) {
  -webkit-animation: line-scale-data 1s cubic-bezier(0.2, 0.68, 0.18, 1.08) -0.2s infinite;
  animation: line-scale-data 1s cubic-bezier(0.2, 0.68, 0.18, 1.08) -0.2s infinite;
}

.ma-line-scale > div:nth-child(4) {
  -webkit-animation: line-scale-data 1s cubic-bezier(0.2, 0.68, 0.18, 1.08) -0.1s infinite;
  animation: line-scale-data 1s cubic-bezier(0.2, 0.68, 0.18, 1.08) -0.1s infinite;
}

.ma-line-scale > div:nth-child(5) {
  -webkit-animation: line-scale-data 1s cubic-bezier(0.2, 0.68, 0.18, 1.08) 0s infinite;
  animation: line-scale-data 1s cubic-bezier(0.2, 0.68, 0.18, 1.08) 0s infinite;
}

@-webkit-keyframes line-scale-data {
  0% {
    -webkit-transform: scaley(1);
    transform: scaley(1);
  }

  50% {
    -webkit-transform: scaley(0.4);
    transform: scaley(0.4);
  }

  to {
    -webkit-transform: scaley(1);
    transform: scaley(1);
  }
}

@keyframes line-scale-data {
  0% {
    -webkit-transform: scaley(1);
    transform: scaley(1);
  }

  50% {
    -webkit-transform: scaley(0.4);
    transform: scaley(0.4);
  }

  to {
    -webkit-transform: scaley(1);
    transform: scaley(1);
  }
}