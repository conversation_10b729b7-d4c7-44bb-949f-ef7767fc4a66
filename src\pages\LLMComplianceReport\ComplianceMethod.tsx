
import { Tabs } from 'antd';

import ComplianceResult from './components/ComplianceResult';
import './ComplianceMethod.less';
// function base64ToUtf8(str) {
//   return decodeURIComponent(
//     Array.prototype.map
//       .call(atob(str), (c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
//       .join(''),
//   );
// }

export default (props: any) => {
  const { template, task } = props;
  console.log('task:', task);
  console.log('templateXXX',template);


  // 判断task是大模型评估页面的行数据task还是ranklist页面的行数据task
  if (task?.id) {
    return (
      <>
        <Tabs
          type="card"
          defaultActiveKey="1"
          size={'large'}
          className="custom-compliance-tabs" 
        >
          {/* 不同合规标准tabs */}
          {template?.map((complianceTemp, index: number) => {
            return (
              <Tabs.TabPane key={complianceTemp.name + index} tab={[complianceTemp.name]}>
                <ComplianceResult
                  classification={complianceTemp}
                  taskID={task.id}
                />
              </Tabs.TabPane>
            );
          })}
        </Tabs>
      </>
    )
  }


};
