import Forbidden from "@/pages/403";

interface Props {
  isPermission?: boolean; // 是否有权限显示，默认为 true
  children: JSX.Element; // 子组件
  cardTitle?: string; // 卡片标题，可选
}

function BasicContent(props: Props) {
  const { isPermission, children, cardTitle } = props;

  return (
    <div className="w-full h-full box-border overflow-auto">
      {isPermission !== false && (
        <div
          id="content"
          className={`
            relative
            box-border
            rounded-4px
          `}
        >
          {cardTitle && ( // 仅当 cardTitle 存在时进行渲染
            <div className="card-title border-bottom h-56px px-24px text-16px flex items-center">
              {cardTitle}
            </div>
          )}
          {<div className="px-20px py-10px">{children}</div>}
        </div>
      )}
      {isPermission === false && <Forbidden />}
    </div>
  );
}

export default BasicContent;
