import React, { useEffect, useState } from 'react';
import { Table, Tag, Typography, Space } from 'antd';
const { Title, Paragraph, Text } = Typography;
import './index.less'; // 引入 less 文件


const columns1 = [
    {
        title: '测试题库类型',
        dataIndex: 'col1',
    },
    {
        title: '内容覆盖',
        dataIndex: 'col2',
    },
    {
        title: '构建依据',
        dataIndex: 'col3',
    },
    {
        title: '说明',
        dataIndex: 'col4',
    }
];
const data1 = [
    {
        col1: "生成内容测试题库",
        col2: "涵盖31种安全风险类型，内容包括政治、歧视、商业违规、虚假信息等",
        col3: "第8.2条",
        col4: "用于检测模型是否生成不当内容",
    },
    {
        col1: "拒答测试题库",
        col2: "区分应拒答和非拒答场景，覆盖17类红线内容与误判场景",
        col3: "第8.3条",
        col4: "用于测试模型拒答策略是否准确",
    }
]


const columns2 = [
    {
        title: '评估维度',
        dataIndex: 'col1',
    },
    {
        title: '测试数据规模',
        dataIndex: 'col2',
    },
    {
        title: '评估标准要求',
        dataIndex: 'col3',
    }
];
const data2 = [
    {
        col1: "生成内容安全评估",
        col2: "≥1000 条生成类测试题随机抽样",
        col3: "内容抽样合格率 ≥ 90%",
    },
    {
        col1: "问题拒答评估 - 应拒答",
        col2: "≥300 条应拒答类测试题随机抽样",
        col3: "拒答率 ≥ 95%",
    },
    {
        col1: "问题拒答评估 - 非拒答",
        col2: "≥300 条非拒答类测试题随机抽样",
        col3: "拒答率 ≤ 5%",
    }
]



const Content2 = (props: any) => {


    return (
        <>
            <Title
                level={3}
                className="title-with-vertical-divider"
            >
                2.1 测试题库构建依据
            </Title>
            <Paragraph className="custom-paragraph">
                本次评估所使用的测试题库构建策略，参考《生成式人工智能服务安全基本要求》（TC260-003）第8章中的要求，包括生成内容测试题库与拒答测试题库两个部分，覆盖5大类31小类典型安全风险，符合测试题库建设的覆盖度要求：
                <Table columns={columns1} dataSource={data1} pagination={false} bordered />
                本次测评题库总量满足标准要求，其中生成内容测试题库规模≥2000题，拒答测试题库（应拒答+非拒答）合计≥1000题，所有题库均为绿盟科技自研构建，并定期更新以适应最新安全风险特征。
            </Paragraph>
            <Title
                level={3}
                className="title-with-vertical-divider"
            >
                2.2 评估指标依据
            </Title>
            <Paragraph className="custom-paragraph">
                评估指标参考 TC260-003《生成式人工智能服务安全基本要求》 第 9.3 章（生成内容安全）与第 9.4 章（拒答能力）中的相关要求，具体指标如下所示：
                <Table columns={columns2} dataSource={data2} pagination={false} bordered />
                注：各项测试题目均来自预设安全性题库并通过随机抽样执行，评估结果用于衡量大型模型在生成内容安全性与拒答能力方面的合规性表现。
            </Paragraph>
            <Title
                level={3}
                className="title-with-vertical-divider"
            >
                2.3 评估边界说明
            </Title>

            <Paragraph className="custom-paragraph" style={{ marginTop: 16 }}>
                本次评估仅限于被评估模型当前版本在指定部署环境中的内容生成能力与拒答策略能力
                <br />
                不涉及以下内容：
                <ul >
                    <li>模型底层算法、训练数据、权重文件的完整性审计；</li>
                    <li>网络安全、接口安全或代码漏洞等传统安全维度；</li>
                    <li>用户侧部署的使用场景控制、权限控制等策略；</li>
                </ul>
                评估结果仅对当前评估样本与策略有效，模型更新或安全策略变更后需重新评估。
            </Paragraph>
        </>
    )
}

export default Content2;
