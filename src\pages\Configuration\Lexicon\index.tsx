import { useTranslation } from 'react-i18next';
import BasicContent from '@/components/Content/BasicContent';
import { getLexiconList, deleteLexicon, createLexicon, updateLexicon } from '@/servers/configuration';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  FooterToolbar,
  ProTable,
} from '@ant-design/pro-components';
import { Button, message, Popconfirm } from 'antd';
import React, { useRef, useState } from 'react';
import AddModalForm from './components/AddModalForm';
function LLModels() {
  const { t } = useTranslation();
  const actionRef = useRef<ActionType>();
  const [selectedRowsState, setSelectedRows] = useState<API.LexiconItem[]>([]);
  const [createModalOpen, setCreateModalOpen] = useState<boolean>(false);
  const [editModalOpen, setEditModalOpen] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.LexiconItem>();

  //删除词库
  const handleRemove = async (selectedRows: any) => {
    const hide = message.loading('正在删除');
    if (!selectedRows) return true;
    try {
      // 对于单个删除，直接使用第一个词库的ID
      if (selectedRows.length === 1) {
        await deleteLexicon(selectedRows[0].id);
      } else {
        // 批量删除时，逐个删除
        for (const row of selectedRows) {
          await deleteLexicon(row.id);
        }
      }
      hide();
      message.success('删除成功');
      // 刷新表格数据
      actionRef.current?.reload();
      return true;
    } catch (error) {
      hide();
      message.error('删除失败，请重试！');
      return false;
    }
  };

  // 创建词库
  const handleAdd = async (fields: any) => {
    const hide = message.loading('正在创建');
    try {
      const response = await createLexicon(fields);
      hide();
      message.success('创建成功');
      setCreateModalOpen(false);
      actionRef.current?.reload();
      return response; // 返回完整响应，包含创建的词库ID
    } catch (error) {
      hide();
      message.error('创建失败，请重试！');
      return false;
    }
  };

  // 更新词库
  const handleUpdate = async (fields: any) => {
    const hide = message.loading('正在更新');
    try {
      if (currentRow?.id) {
        const response = await updateLexicon(currentRow.id, fields);
        hide();
        message.success('更新成功');
        setEditModalOpen(false);
        actionRef.current?.reload();
        return response; // 返回完整响应
      }
    } catch (error) {
      hide();
      message.error('更新失败，请重试！');
      return false;
    }
  };

  // 编辑词库
  const handleEdit = (record: API.LexiconItem) => {
    setCurrentRow(record);
    setEditModalOpen(true);
  };

  const columns: ProColumns<API.LexiconItem>[] = [
    {
      title: '序号',
      dataIndex: 'id',
      valueType: 'indexBorder',
      width: 60,
      hideInSearch: true,
    },
    {
      title: "词库名称",
      dataIndex: 'name',
      search: {
        transform: (value) => ({ name: value }),
      },

    },
    {
      title: "描述",
      dataIndex: 'description',
      search: false,
      ellipsis: true,
    },
    {
      title: "标签",
      dataIndex: 'tag',
      search: false,
      render: (_, record) => {
        const tags = record.tag;
        if (!tags || tags.length === 0) {
          return '-';
        }
        return tags.join(', ');
      }
    },
    {
      title: "词条数",
      dataIndex: 'word_count',
      search: false
    },
    {
      title: "创建时间",
      dataIndex: 'create_time',
      valueType: "dateTime",
      search: false
    },

    {
      title: "操作",
      dataIndex: 'option',
      valueType: 'option',
      width: '15%',
      render: (_, record) => [
        <a key="edit" onClick={() => handleEdit(record)} style={{ marginRight: 8 }}>
          <EditOutlined /> 编辑
        </a>,
        <Popconfirm
          key="delete"
          title={`确认删除词库 "${record.name}" 吗？此操作不可逆！`}
          onConfirm={() => {
            handleRemove([record]);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
          okText="确认"
          cancelText="取消"
        >
          <a>
            删除
          </a>
        </Popconfirm>
      ],
    },
  ];
  return (
    <div className="py-16px px-16px">
      <BasicContent isPermission={true}>
        <ProTable<API.LexiconItem>
          actionRef={actionRef}
          rowKey="id"
          search={{
            labelWidth: 120,
          }}
          toolBarRender={() => [
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                setCreateModalOpen(true);
              }}
            >
              <PlusOutlined />创建词库
            </Button>,
          ]}
          request={async (params) => {
            try {
              const newParams = {
                limit: params.pageSize || 10,
                offset: ((params.current || 1) - 1) * (params.pageSize || 10),
                keyword: params.name || '',
              };
              const response = await getLexiconList(newParams);
              return {
                data: response.data || [],
                total: response.total || 0,
                success: true
              };
            } catch (error) {
              console.error('获取词库列表失败:', error);
              message.error('获取词库列表失败');
              return {
                data: [],
                total: 0,
                success: false
              };
            }
          }}
          columns={columns}
          rowSelection={{
            onChange: (_, selectedRows) => {
              setSelectedRows(selectedRows);
            },
          }}
        />
        {selectedRowsState?.length > 0 && (
          <FooterToolbar
            extra={
              <div>
                已选择
                <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>{' '}
                项
              </div>
            }
          >
            <Button
              onClick={async () => {
                await handleRemove(selectedRowsState);
                setSelectedRows([]);
                actionRef.current?.reloadAndRest?.();
              }}
              danger
            >
              批量删除
            </Button>
          </FooterToolbar>
        )}

        {/* 创建词库模态框 */}
        <AddModalForm
          modalOpen={createModalOpen}
          onCancel={() => {
            setCreateModalOpen(false);
            setCurrentRow(undefined);
          }}
          onSubmit={handleAdd}
        />

        {/* 编辑词库模态框 */}
        <AddModalForm
          modalOpen={editModalOpen}
          onCancel={() => {
            setEditModalOpen(false);
            setCurrentRow(undefined);
          }}
          onSubmit={handleUpdate}
          currentRow={currentRow}
        />
      </BasicContent>
    </div>
  );
}

export default LLModels;