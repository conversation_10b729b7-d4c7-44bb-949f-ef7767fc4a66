import React, { useEffect, useState } from "react";
import passedpng from '@/static/assets/passed.png';
import scanpng from '@/static/assets/scan.png';
import unpasspng from '@/static/assets/unpass.png';
import passratepng from '@/static/assets/passrate.png';
import { StatisticCard } from '@ant-design/pro-components';
import { getTaskReportSummaryDetail } from "@/servers/report";
import { ProCard } from "@ant-design/pro-components";
import CategoriesRadar from "./CategoriesRadar";
import { Tag } from "antd";

const imgStyle = {
  display: 'block',
  width: 42,
  height: 42,
};


// 通过面板组件
const PassingRateStatisticComptPanel = (props: any) => {
  // const { total, pass, fail } = props.dimension;
  const [passData, setPassData] = useState({
    total: 0,
    pass: 0,
    fail: 0
  })
  const [subScore, setSubScore] = useState([])
  const { classification, taskID, qualification_rate_standard, show_categories_column } = props;
  console.log(" PassingRateStatisticCompt   parentName:", classification?.name, "   model:", classification);

  const initReportDetail = async () => {

    const { data } = await getTaskReportSummaryDetail({
      task_id: taskID,
      classification_id: classification?.id,
      limit: 0,
      offset: 1
    })
    // // 合并统计
    const passD = {
      total: 0,
      pass: 0,
      fail: 0
    }
    const subRateScore = []
    data?.forEach(item => {
      passD.total += item.missions_count
      passD.pass += item.pass_count
      passD.fail += item.fail_count + item.error_count
      subRateScore.push({
        name: item.name,
        score: (() => {
          const raw = parseFloat(item.pass_rate || 0);
          return Number.isInteger(raw) ? raw : parseFloat(raw.toFixed(2));
        })()
      })
    });
    setSubScore(subRateScore)
    setPassData(passD)
  }

  useEffect(() => {
    initReportDetail()
  }, [taskID])
  return (
    <ProCard
      // title="数据面板"
      // size="small"
      split="vertical"
    >
      <ProCard colSpan={'30%'}>
        {/* <StatisticCard.Group direction={'row'}>
            <StatisticCard
              statistic={{
                title: '总数',
                value: passData?.total,
                icon: <img style={imgStyle} src={scanpng} alt="icon" />,
              }}
            />
            <StatisticCard
              statistic={{
                title: '合格率',
                value: (passData?.pass / passData?.total * 100).toFixed(2) + '%' || 0,
                icon: <img style={imgStyle} src={passratepng} alt="icon" />,
              }}
            />
          </StatisticCard.Group>
          <StatisticCard.Group direction={'row'}>
            <StatisticCard
              statistic={{
                title: '通过',
                value: passData?.pass,
                icon: <img style={imgStyle} src={passedpng} alt="icon" />,
              }}
            />
            <StatisticCard
              statistic={{
                title: '未通过',
                value: passData?.fail,
                icon: <img style={imgStyle} src={unpasspng} alt="icon" />,
              }}
            />
          </StatisticCard.Group> */}


        <StatisticCard.Group direction={'column'}>
          <StatisticCard
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              background: '#f7f8fa',
              borderRadius: '4px'
            }}
            statistic={{
              title: '合格率',
              value: (passData?.pass / passData?.total * 100).toFixed(2) + '%' || 0,
              // icon: <img style={imgStyle} src={passratepng} alt="icon" />,
            }}
          />
          {passData?.total > 0 && (
            <Tag
              style={{
                position: 'absolute',
                top: 0,
                right: -9,
                zIndex: 1
              }}
              // 根据合格率动态设置颜色
              color={((passData.pass / passData.total) * 100) >= 90 ? "#68b92e" : "#de5454"}
            >
              {/* 根据合格率动态设置文字 */}
              {((passData.pass / passData.total) * 100) >= 90 ? "通过" : "未通过"}
            </Tag>
          )}
          <ProCard split="horizontal" bordered style={{ marginTop: 10, marginBottom: 10 }}>
            <ProCard split="vertical">
              <ProCard colSpan={"40%"}
                style={{
                  background: '#f7f8fa',
                  borderRadius: 0,
                  fontFamily: 'PingFang SC, PingFang SC-Regular',
                  fontWeight: 400,
                  textAlign: 'left',
                  color: '#535353'
                }}>测试数据总数</ProCard>
              <ProCard
                style={{
                  fontSize: '14px',
                  fontFamily: 'Roboto, Roboto-Medium',
                  // Medium 对应的 font-weight 值是 500
                  fontWeight: 500,
                  textAlign: 'left',
                  color: '#535353'
                }}
              >{passData?.total}</ProCard>
            </ProCard>
            <ProCard split="vertical">
              <ProCard colSpan={"40%"}
                style={{
                  background: '#f7f8fa',
                  borderRadius: 0,
                  fontFamily: 'PingFang SC, PingFang SC-Regular',
                  fontWeight: 400,
                  textAlign: 'left',
                  color: '#535353'
                }}>通过</ProCard>
              <ProCard
                style={{
                  fontSize: '14px',
                  fontFamily: 'Roboto, Roboto-Medium',
                  // Medium 对应的 font-weight 值是 500
                  fontWeight: 500,
                  textAlign: 'left',
                  color: '#535353'
                }}>{passData?.pass}</ProCard>
            </ProCard>
            <ProCard split="vertical">
              <ProCard colSpan={"40%"}
                style={{
                  background: '#f7f8fa',
                  borderRadius: 0,
                  fontFamily: 'PingFang SC, PingFang SC-Regular',
                  fontWeight: 400,
                  textAlign: 'left',
                  color: '#535353'
                }}>未通过</ProCard>
              <ProCard
                style={{
                  fontSize: '14px',
                  fontFamily: 'Roboto, Roboto-Medium',
                  // Medium 对应的 font-weight 值是 500
                  fontWeight: 500,
                  textAlign: 'left',
                  color: '#DE5454'
                }}>{passData?.fail}</ProCard>
            </ProCard>

          </ProCard>
        </StatisticCard.Group>

      </ProCard>
      <ProCard style={{ height: 240, padding: 0 }}>
        <CategoriesRadar subScore={subScore} />
      </ProCard>
    </ProCard>
  );
};

export default PassingRateStatisticComptPanel;