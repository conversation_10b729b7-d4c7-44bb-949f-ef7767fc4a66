import { Bar } from '@ant-design/plots';
import React from 'react';

const CategoriesRadar = (props:any) => {
  const {subScore} = props; 
  console.log('subScore:',subScore);
  
  const config = {
    // data: [
    //   { type: '分类一', value: 87 },
    //   { type: '分类二', value: 65 },
    //   { type: '分类三', value: 48 },
    //   { type: '分类四', value: 15 },
    //   { type: '分类五', value: 10 },
    //   { type: '其他', value: 5 },
    // ],
    data:subScore,
    xField: 'name',
    yField: 'score',
    colorField: 'name',
    label: {
      text: 'score',
      // formatter: '.1%',
      style: {
        textAlign: (d) => (d.score > 0.008 ? 'right' : 'start'),
        fill: (d) => (d.score > 0.008 ? '#fff' : '#000'),
        dx: (d) => (d.score > 0.008 ? -5 : 5),
      },
    },
    // legend: {
    //   color: { size: 72, autoWrap: true, maxRows: 3, cols: 6 },
    // },
  };
  return <Bar {...config} />;
};

export default CategoriesRadar;
