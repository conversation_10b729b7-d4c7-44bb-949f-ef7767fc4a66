{"name": "nsfocus-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "build-only": "vite build", "lint": "eslint . --ext .js,.ts,.jsx,.tsx --fix", "lint:stylelint": "stylelint --cache --fix \"**/*.{jsx,tsx,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.6.1", "@ant-design/cssinjs": "^1.5.6", "@ant-design/icons": "^4.7.0", "@ant-design/plots": "^2.6.2", "@ant-design/pro-components": "^2.8.10", "@reduxjs/toolkit": "^1.8.5", "@wangeditor/editor": "^5.1.21", "@wangeditor/editor-for-react": "^1.0.5", "ahooks": "^3.7.1", "antd": "^5.9.0", "axios": "^1.4.0", "classnames": "^2.5.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.6", "echarts": "^5.3.3", "i18next": "^23.4.4", "i18next-browser-languagedetector": "^7.1.0", "i18next-http-backend": "^2.2.2", "nprogress": "^0.2.0", "rc-resize-observer": "^1.2.1", "react": "^18.2.0", "react-activation": "0.12.4", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-i18next": "^13.1.2", "react-markdown": "^9.0.1", "react-redux": "^8.0.5", "react-resizable": "^3.0.4", "react-router-dom": "6.7.0", "react-syntax-highlighter": "^15.5.0", "react-virtualized-auto-sizer": "^1.0.7", "react-window": "^1.8.7", "remark-gfm": "^4.0.0"}, "devDependencies": {"@commitlint/cli": "^17.0.3", "@commitlint/config-conventional": "^17.0.3", "@iconify/react": "^3.2.2", "@loadable/component": "^5.15.3", "@remix-run/router": "^1.0.2", "@types/crypto-js": "^4.1.1", "@types/loadable__component": "^5.13.4", "@types/node": "^18.7.9", "@types/nprogress": "^0.2.0", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@types/react-resizable": "^3.0.3", "@types/react-virtualized-auto-sizer": "^1.0.1", "@types/react-window": "^1.8.5", "@typescript-eslint/eslint-plugin": "^5.35.1", "@typescript-eslint/parser": "^5.35.1", "@unocss/preset-attributify": "^0.45.9", "@unocss/preset-icons": "^0.45.9", "@vitejs/plugin-legacy": "^4.0.2", "@vitejs/plugin-react-swc": "^3.3.0", "cross-env": "^7.0.3", "eslint": "^8.22.0", "eslint-plugin-react": "^7.31.0", "eslint-plugin-react-hooks": "^4.6.0", "globby": "^13.1.3", "husky": "^8.0.1", "jsdom": "^20.0.3", "less": "^4.1.3", "postcss": "^8.4.19", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "rc-picker": "^2.6.10", "rollup-plugin-external-globals": "^0.7.1", "rollup-plugin-visualizer": "^5.8.2", "stylelint": "^14.15.0", "stylelint-config-prettier": "^9.0.4", "stylelint-config-recommended": "^9.0.0", "stylelint-config-recommended-less": "^1.0.4", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "terser": "^5.19.4", "typescript": "^4.9.3", "unocss": "^0.45.9", "vite": "^4.4.7", "vite-plugin-compression": "^0.5.1", "vite-plugin-purge-icons": "^0.9.0", "vite-plugin-windicss": "^1.8.8"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}