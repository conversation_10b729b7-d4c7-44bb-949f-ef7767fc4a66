import { useEffect } from 'react';
import { App } from 'antd';
import { useTranslation } from 'react-i18next';
import { HashRouter as Router } from 'react-router-dom';
import nprogress from 'nprogress';
import AppPage from './App';
import StaticAntd from '@/utils/staticAntd';

// antd
import { ConfigProvider, theme } from "antd";
import zhCN from 'antd/es/locale/zh_CN';
import enUS from 'antd/es/locale/en_US';
import THEME_DEFAULT from "@/assets/css/antd-token/theme-default";
import THEME_GA from "@/assets/css/antd-token/theme-ga";
import THEME_DARK from "@/assets/css/antd-token/theme-dark";

// antd主题
// keepalive
import { AliveScope } from 'react-activation';

import { useCommonStore } from '@/hooks/useCommonStore';

// antd主题
const THEME_CONFIG = {
  default: THEME_DEFAULT,
  dark: THEME_DARK,
  ga: THEME_GA,
};

function Page() {
  const { i18n } = useTranslation();
  const { themeAntd } = useCommonStore();
  // 获取当前语言
  const currentLanguage = i18n.language;

  // 顶部进度条
  useEffect(() => {
    nprogress.done();

    // 关闭loading
    const firstElement = document.getElementById('first');
    if (firstElement && firstElement.style?.display !== 'none') {
      firstElement.style.display = 'none';
    }

    return () => {
      nprogress.start();
    };
  }, []);

  return (
    <Router>
      <ConfigProvider
        locale={currentLanguage === "en" ? enUS : zhCN}
        theme={{
          token: THEME_CONFIG[themeAntd]?.token,
          components: THEME_CONFIG[themeAntd]?.components,
          algorithm: themeAntd === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
          // algorithm: theme.compactAlgorithm,  // 紧凑模式，在内容较多，屏幕分辨率较小的时候使用
        }}
      >
        <App>
          <StaticAntd />
          <AliveScope>
            <AppPage />
          </AliveScope>
        </App>
      </ConfigProvider>
    </Router>
  );
}

export default Page;