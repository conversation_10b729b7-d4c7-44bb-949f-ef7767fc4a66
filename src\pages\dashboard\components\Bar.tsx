import type { EChartsCoreOption } from 'echarts';
import { useEffect } from 'react';
// import { useTranslation } from 'react-i18next';
import { useEcharts } from '@/hooks/useEcharts';
import { useCommonStore } from '@/hooks/useCommonStore';

const data = [
  962,
  1023,
  1112,
  1123,
  1239,
  1382,
  1420,
  1523,
  1622,
  1643,
  1782,
  1928,
];

function Bar() {
  let colorList = ["#68b92e", "#FA8C16"];
  // const { t } = useTranslation();
  const { permissions } = useCommonStore();
  const option: EChartsCoreOption = {
    // title: {
    //   text: t("dashboard.rechargeRankingDay"),
    //   left: 30,
    //   top: 5,
    // },
    color: colorList,
    tooltip: {
      trigger: "axis",
    },
    legend: {
      show: false,
      color: "#7E7E7E",
    },
    grid: {
      top: "15%",
      left: "3%",
      right: "3%",
      bottom: "11%",
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        data: ["草堂镇", "白帝镇", "朱衣镇", "康乐镇", "永乐镇", "安坪镇"],
        axisLine: {
          show: false,
          lineStyle: {
            color: "#063374",
            width: 1,
            type: "solid",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: "#7E7E7E",
          fontSize: 12,
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        axisLabel: {
          formatter: "{value}",
          textStyle: {
            color: "#7E7E7E",
            fontSize: 12,
          },
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
          },
        },
      },
    ],
    series: [
      {
        name: "好",
        type: "bar",
        data: [90, 95, 120, 110, 98, 130],
        barWidth: 12, //柱子宽度
        barGap: 1, //柱子之间间距
        itemStyle: {
          barBorderRadius: [4, 4, 0, 0],
        },
      },
    ],
  };

  const [echartsRef, init] = useEcharts(option, data);

  useEffect(() => {
     if (permissions.length) {
       setTimeout(() => {
         init();
       }, 100);
     }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [echartsRef]);
  
  return (
    <div className="h-350px">
      <div ref={echartsRef} className="w-full h-full"></div>
    </div>
  );
}

export default Bar;