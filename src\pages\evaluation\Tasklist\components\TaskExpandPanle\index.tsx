import { Statistic, StatisticCard } from "@ant-design/pro-components";
import { Card, Col, Divider, Row, Space, Tag } from "antd";
import { Typography, Splitter, Flex } from 'antd';
const { Title, Paragraph, Text, Link } = Typography;
import { React, useEffect, useState } from "react";
import { ProDescriptions } from '@ant-design/pro-components';
import <PERSON><PERSON><PERSON> from "../PieChart";
import { getTestllms } from "@/servers/llms";
import { getTastStatistics } from "@/servers/evaluation";
import taskpng from '@/assets/images/task.png';
import statuspng from '@/assets/images/status.png';
import evalpng from '@/assets/images/eval.png';


const imgStyle = {
    display: 'block',
    width: 24,
    height: 24,
};

const TaskExpandPanle = (props: any) => {
    const [model, setModel] = useState()
    const [statistics, setStatistics] = useState({})
    const [testStatistics, setTestStatistics] = useState({
        request: 0,
        response: 0,
        error: 0
    })
    const [evaluateStatistics, setEvaluateStatistics] = useState({
        request: 0,
        response: 0,
        error: 0
    })
    const { task } = props;
    console.log("task:", task);
    useEffect(() => {
        // 获取模型数据
        getTestllms({ id: task?.model_id }).then((res) => {
            setModel(res.data);
        });
        // 获取静态统计数据
        getTastStatistics(task?.id).then((res) => {
            console.log("res", res);
            setStatistics(res.data);
        });
    }, [task]);
    return (
        <>
            <Splitter style={{ height: 220, boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)' }}>
                <Splitter.Panel>
                    <Space style={{ margin: 10 }}><img style={imgStyle} src={taskpng} alt="icon" /><Title style={{ margin: 4 }} level={5}>任务信息</Title ></Space>
                    <ProDescriptions style={{ margin: 10 }} column={2} >
                        <ProDescriptions.Item
                            label="任务名称">{task?.name} </ProDescriptions.Item>
                        <ProDescriptions.Item
                            label="发包">{statistics?.mission_count?.completed + ` / ` + statistics?.total} <Tag bordered={false} color="success">
                                {statistics?.status}
                            </Tag></ProDescriptions.Item>
                        <ProDescriptions.Item
                            label="测试模型">{model?.name}</ProDescriptions.Item>

                        <ProDescriptions.Item
                            label="Model">{model?.internal_option?.model}</ProDescriptions.Item>
                        <ProDescriptions.Item
                            label="API URL">{model?.api_url}</ProDescriptions.Item>
                    </ProDescriptions>
                </Splitter.Panel>
                <Splitter.Panel>
                    <Space style={{ margin: 10 }}><img style={imgStyle} src={evalpng} alt="icon" /><Title style={{ margin: 4 }} level={5}>当前评估</Title ></Space>
                    <ProDescriptions title="" style={{ margin: 10 }} column={3} >
                        <ProDescriptions.Item
                            label="综合分数">{(statistics?.evaluate_count?.passed / statistics?.total * 100).toFixed(2)}</ProDescriptions.Item>
                        <ProDescriptions.Item
                            label="通过率">{(statistics?.evaluate_count?.passed / statistics?.total * 100).toFixed(2)}% </ProDescriptions.Item>
                        <ProDescriptions.Item
                            label="当前状态">
                            {(statistics?.evaluate_count?.passed / statistics?.total * 100).toFixed(2) > 90 ? <Tag color="success">安全</Tag> : <Tag color="error">不安全</Tag>}
                        </ProDescriptions.Item>
                        <ProDescriptions.Item
                            label="评估方法">{task?.evaluate_method?.name} </ProDescriptions.Item>
                        {
                            task?.evaluate_method?.name === "llm" && <ProDescriptions.Item
                                label="评估模型">{task?.evaluate_method?.args?.[0]?.Value} </ProDescriptions.Item>
                        }

                    </ProDescriptions>

                </Splitter.Panel>
                <Splitter.Panel>
                    <Space style={{ margin: 10 }}><img style={imgStyle} src={statuspng} alt="icon" /><Title style={{ margin: 4 }} level={5}>测试 Tokens</Title ></Space>
                    <ProDescriptions title="" style={{ margin: 10 }} column={3} >
                        <ProDescriptions.Item
                            label="请求">{testStatistics.request}</ProDescriptions.Item>
                        <ProDescriptions.Item
                            label="返回">{testStatistics.response} </ProDescriptions.Item>
                        <ProDescriptions.Item
                            label="错误">{testStatistics.error}</ProDescriptions.Item>
                    </ProDescriptions>
                    <Space style={{ margin: 10 }}><img style={imgStyle} src={statuspng} alt="icon" /><Title style={{ margin: 4 }} level={5}>评估 Tokens</Title ></Space>
                    <ProDescriptions title="" style={{ margin: 20 }} column={3} >
                        <ProDescriptions.Item
                            label="请求">{evaluateStatistics.request}</ProDescriptions.Item>
                        <ProDescriptions.Item
                            label="返回">{evaluateStatistics.response} </ProDescriptions.Item>
                        <ProDescriptions.Item
                            label="错误">{evaluateStatistics.error}</ProDescriptions.Item>
                    </ProDescriptions>
                </Splitter.Panel>
            </Splitter>
        </>
    );

}

export default TaskExpandPanle;