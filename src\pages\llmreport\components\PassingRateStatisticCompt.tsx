import React, { useEffect, useState } from "react";
import { getTaskReportSummaryDetail } from "@/servers/report";
import { ProCard } from "@ant-design/pro-components";
import CategoriesColumn from "./CategoriesColumn";

const imgStyle = {
  display: 'block',
  width: 42,
  height: 42,
};


// 通过面板组件
const PassingRateStatisticCompt = (props: any) => {
  const [passData, setPassData] = useState({
    total: 0,
    pass: 0,
    fail: 0
  })
  const [columnData, setColumnData] = useState([])
  const [loading, setLoading] = useState(false)
  const { classification, taskID, qualification_rate_standard } = props;
  // console.log(" qualification_rate_standard   ",parentName,"   model:",qualification_rate_standard);

  const initReportDetail = async () => {
    const { data } = await getTaskReportSummaryDetail({
      task_id: taskID,
      classification_id: classification?.id,
      limit: 10,
      offset: 1
    })
    // 合并统计
    const passD = {
      total: 0,
      pass: 0,
      fail: 0
    }
    const newColumnData = []
    data?.forEach((item: any) => {
      passD.total += item.missions_count
      passD.pass += item.pass_count
      passD.fail += item.fail_count + item.error_count
      newColumnData.push({
        name: item.name,
        rate: (() => {
          const raw = parseFloat(item.pass_rate || 0);
          return Number.isInteger(raw) ? raw : parseFloat(raw.toFixed(2));
        })()
      })
    });

    setPassData(passD);
    setColumnData(newColumnData);
  }

  useEffect(() => {
    initReportDetail()
  }, [taskID])

  return (
    <>
      <ProCard split='vertical' key={`key-summary` + classification?.name} style={{ marginBottom: 10, marginTop: 5 }} boxShadow >
        <ProCard colSpan={"30%"}>
          <ProCard split="horizontal" bordered>
            <ProCard style={{
                  background: '#f7f8fa',
                  borderRadius: 0,
                  fontFamily: 'PingFang SC, PingFang SC-Regular',
                  fontWeight: 400,
                  textAlign: 'left',
                  color: '#535353',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}>{classification.name_alias || classification.name}</ProCard>
            <ProCard split="vertical">
              <ProCard colSpan={"40%"}
                style={{
                  background: '#f7f8fa',
                  borderRadius: 0,
                  fontFamily: 'PingFang SC, PingFang SC-Regular',
                  fontWeight: 400,
                  textAlign: 'left',
                  color: '#535353'
                }}>合格率</ProCard>
              <ProCard
                style={{
                  fontSize: '14px',
                  fontFamily: 'Roboto, Roboto-Medium',
                  // Medium 对应的 font-weight 值是 500
                  fontWeight: 500,
                  textAlign: 'left',
                  color: '#535353'
                }}
              >{(passData?.pass / passData?.total * 100).toFixed(2) + '%' || 0}</ProCard>
            </ProCard>
            <ProCard split="vertical">
              <ProCard colSpan={"40%"}
                style={{
                  background: '#f7f8fa',
                  borderRadius: 0,
                  fontFamily: 'PingFang SC, PingFang SC-Regular',
                  fontWeight: 400,
                  textAlign: 'left',
                  color: '#535353'
                }}>测试数据总数</ProCard>
              <ProCard
                style={{
                  fontSize: '14px',
                  fontFamily: 'Roboto, Roboto-Medium',
                  // Medium 对应的 font-weight 值是 500
                  fontWeight: 500,
                  textAlign: 'left',
                  color: '#535353'
                }}
              >{passData?.total}</ProCard>
            </ProCard>
            <ProCard split="vertical">
              <ProCard colSpan={"40%"}
                style={{
                  background: '#f7f8fa',
                  borderRadius: 0,
                  fontFamily: 'PingFang SC, PingFang SC-Regular',
                  fontWeight: 400,
                  textAlign: 'left',
                  color: '#535353'
                }}>通过</ProCard>
              <ProCard
                style={{
                  fontSize: '14px',
                  fontFamily: 'Roboto, Roboto-Medium',
                  // Medium 对应的 font-weight 值是 500
                  fontWeight: 500,
                  textAlign: 'left',
                  color: '#535353'
                }}>{passData?.pass}</ProCard>
            </ProCard>
            <ProCard split="vertical">
              <ProCard colSpan={"40%"}
                style={{
                  background: '#f7f8fa',
                  borderRadius: 0,
                  fontFamily: 'PingFang SC, PingFang SC-Regular',
                  fontWeight: 400,
                  textAlign: 'left',
                  color: '#535353'
                }}>未通过</ProCard>
              <ProCard
                style={{
                  fontSize: '14px',
                  fontFamily: 'Roboto, Roboto-Medium',
                  // Medium 对应的 font-weight 值是 500
                  fontWeight: 500,
                  textAlign: 'left',
                  color: '#DE5454'
                }}>{passData?.fail}</ProCard>
            </ProCard>

          </ProCard>

        </ProCard>
        <ProCard key={`key_show_categories_column` + classification.name} style={{ marginBottom: 10, marginTop: 5 }} >
          <CategoriesColumn key={`key-column` + classification.name} columnData={columnData} qualification_rate_standard={qualification_rate_standard} />
        </ProCard>


      </ProCard>

    </>

  );
};

export default PassingRateStatisticCompt;