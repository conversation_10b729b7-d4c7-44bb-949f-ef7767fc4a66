@import url("./default.less");

.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab,
.ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab {
  padding: 5px 16px 8px !important;
}

/* 全屏:开始 */
.full-modal {
  .ant-modal {
    position: absolute;
    max-width: 100%;
    top: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }

  .ant-modal-body {
    flex: 1;
  }
}

/* 全屏:结束 */

/* 表格列拖拽:开始 */
.react-resizable {
  position: relative;
  background-clip: padding-box;
}

.react-resizable-handle {
  position: absolute;
  right: -5px;
  bottom: 0;
  z-index: 1;
  width: 10px;
  height: 100%;
  cursor: col-resize;
}

.login-box .ant-input {
  color: black;
  border-color: #ccc;
  background-color: #fff;
}
.login-box .ant-input-affix-wrapper {
  color: black;
  border-color: #ccc;
  background-color: #fff;
}
.theme-dark {
  .border-bottom {
    border-bottom: 1px solid @darkBorderColor !important;
  }
  .divide-solid {
    border-left: 1px solid @darkBorderColor!important;
  }
  .bg-black {
    background: #26303a;
  }
  #header {
    border-left: 1px solid #000000;
    border-bottom: 1px solid #000000 !important;
  }
  // .ant-tabs-tab {
  //   background: @darkContentBg !important;
  // }
  // .ant-tabs-tab-active {
  //   background: #000000 !important;
  // }
}
