import type { FormData } from "#/form";
import { JSXElementConstructor, ReactElement, ReactNode, ReactPortal, useCallback, useEffect, useState } from "react";
import { getDataTrends } from "@/servers/dashboard";
import { useUnactivate } from "react-activation";
import { useTranslation } from "react-i18next";
import BasicContent from "@/components/Content/BasicContent";
import Bar from "./components/Bar";
import Line from "./components/Line";
import Block from "./components/Block";
import { Col, Row, Table } from "antd";
import MultBar from "./components/MultBar";
import Pie from "./components/Pie";
import { tableData } from "./model";
import classNames from "classnames";
import "./index.less";

// 初始化搜索
const initSearch = {
  pay_date: ["2022-10-19", "2022-10-29"],
};

function Dashboard() {
  const { t } = useTranslation();
  const [isLoading, setLoading] = useState(false);

  /**
   * 搜索提交
   * @param values - 表单返回数据
   */
  const handleSearch = useCallback(async (values: FormData) => {
    // 数据转换
    values.all_pay = values.all_pay ? 1 : undefined;

    const query = { ...values };
    try {
      setLoading(true);
      await getDataTrends(query);
    } finally {
      setLoading(false);
    }
  }, []);
  const columns = [
    {
      title: "序号",
      dataIndex: "key",
      key: "key",
    },
    {
      title: "事件名称",
      dataIndex: "name",
      key: "name",
      render: (
        text:
          | string
          | number
          | boolean
          | ReactElement<any, string | JSXElementConstructor<any>>
          | Iterable<ReactNode>
          | ReactPortal
          | null
          | undefined
      ) => <a>{text}</a>,
    },
    {
      title: "风险等级",
      dataIndex: "status",
      key: "status",
      render: (
        text:
          | string
          | number
          | boolean
          | ReactElement<any, string | JSXElementConstructor<any>>
          | Iterable<ReactNode>
          | null
          | undefined
      ) => (
        <div
          className={classNames({
            "level-font": true,
            "level-0": text === "安",
            "level-1": text === "低",
            "level-2": text === "中",
            "level-3": text === "高",
            "level-4": text === "危",
          })}
        >
          {text}
        </div>
      ),
    },
    {
      title: "描述",
      key: "content",
      dataIndex: "content",
    },
  ];
  useEffect(() => {
    handleSearch(initSearch);
  }, [handleSearch]);

  useUnactivate(() => {
    console.log("退出时执行");
  });

  return (
    <div className="px-16px">
      <Row gutter={16} className="my-16px">
        <Col className="gutter-row" span={24}>
          <BasicContent isPermission={true}>
            <Block />
          </BasicContent>
        </Col>
      </Row>
      <Row gutter={20} className="my-16px">
        <Col span={8}>
          <BasicContent cardTitle={t("dashboard.rechargeRankingDay")}>
            <Pie />
          </BasicContent>
        </Col>
        <Col span={8}>
          <BasicContent cardTitle={t("dashboard.dataCount")}>
            <Bar />
          </BasicContent>
        </Col>
        <Col span={8}>
          <BasicContent cardTitle={t("dashboard.dataTrends")}>
            <Line />
          </BasicContent>
        </Col>
      </Row>
      <Row gutter={20}>
        <Col span={12}>
          <BasicContent cardTitle={t("dashboard.dataTrends")}>
            <div className="table-box">
              <Table
                columns={columns}
                dataSource={tableData}
                pagination={false}
                size="middle"
              />
            </div>
          </BasicContent>
        </Col>
        <Col span={12}>
          <BasicContent cardTitle={t("dashboard.dataCount")}>
            <MultBar />
          </BasicContent>
        </Col>
      </Row>
    </div>
    // <BasicContent isPermission={true}>
    //   <>
    //     <BasicSearch
    //       list={searchList(t)}
    //       data={initSearch}
    //       isLoading={isLoading}
    //       isCreate={false}
    //       handleFinish={handleSearch}
    //     />

    //     <div className='py-10px'>
    //       <Block />
    //     </div>

    //     <div className='flex justify-between w-full'>
    //       <Line />
    //       <Bar />
    //     </div>
    //   </>
    // </BasicContent>
  );
}

export default Dashboard;
