
declare namespace API {
  type ApiResponse = {
    code?: number;
    type?: string;
    message?: string;
  };

  // 用例数据集列表
  type DictList = {
    status?: number;
    data?: DictItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: string;
  };
  // 树形结构
  type Category = {
    category_name: string;
    category_alias: string;
    category_desc: string;
    sub_categories?: Category[]; // 数组，支持递归嵌套
  }
  // 用例列表
  type UsecaseList = {
    status?: number;

    data?: UsecaseItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: string;
  }

  // 评估方法
  type EvaluateMethod = {
    name: string; // 评估方法名称
    args: string[]; // 评估参数数组
  };
  // 路径
  type Path = {
    id: string; // ID编号
    name: string; // 名称
    [property: string]: any;
  }
  // 分类
  type Classification = {
    id: string; // ID
    name: string; // 名称
    description?: string; // 描述
    value: string; // 值
    qrs: { [key: string]: any }; // 其他属性
    module: string; // 模块
    path: Path[]; // 路径数组，包含多个路径对象
    parent_id: string; // 父级ID
    parent_name: string; // 父级名称
    color: string; // <hex>标签颜色 hex
    order: number; // 同级排序由小到大视为0
    created_time: string; // 创建时间
    updated_time: string; // 更新时间
  }
  // 用例
  type UsecaseItem = {
    id: string;//iid编号
    name: string; // 用例名称
    payload: string; // 用例内容
    description: string; // 用例描述
    evaluate_methods: EvaluateMethod[]; // 评估方法
    classification: Classification[]; // 分类
    create_time: string; // 创建时间
    update_time: string; // 更新时间  
  }

  /**
   * 字典
   */
  type DictItem = {
    /**
     * 标签颜色 hex
     */
    color: string;
    /**
     * 详细描述
     */
    describe: string;
    /**
     * ID 编号
     */
    id: string;
    /**
     * 名称
     */
    name: string;
    /**
     * 同级排序，由大到小，留空视为0
     */
    order: number;
    /**
     * 按顺序记录上级字典
     */
    path: Path[];
  }


  type LexiconList = {
    data: LexiconItem[];
    total: number;
  }

  type LexiconItem = {
    id?: number | string;
    name?: string;
    description?: string;
    tag?: string[];
    word_count?: number;
    create_time?: string;
    update_time?: string;
  }

  // 词条相关类型 - 根据实际API响应调整
  type WordList = {
    status: number;
    message: string;
    data: WordItem[];
    total?: number;
  }

  type WordItem = {
    id: string;
    word: string;
    rule_class: string;
    tag: string;
  }

  // 词条分类相关类型 - 根据实际API响应调整
  type WordCategoryList = {
    status: number;
    message: string;
    data: WordCategoryItem[];
  }

  type WordCategoryItem = {
    rule_class: string;
    tag: string[];
  }

  type Response = {
    success?: boolean;
    message?: string;
    data?: any;
  }

}