{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": false,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noUnusedLocals": true, // 有未使用的变量时，抛出错误
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "types": [
      "vite/client",
      "node"
    ],
    "paths": {
      "@/*": ["src/*"],
      "#/*": ["types/*"]
    }
  },
  "include": ["src"],
  "exclude": ["dist", "node_modules", "cypress"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
