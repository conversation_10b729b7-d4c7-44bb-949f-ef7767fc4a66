import { FileDoneOutlined, FileOutlined, FileTextOutlined, ProfileOutlined } from '@ant-design/icons';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Card, Col, ConfigProvider, Input, Row, Statistic, Tree } from 'antd';
const OverView = () => {
    return (
        <PageContainer title={false}>
            {/* <Col span={12}></Col> */}
            <Card>
                <Row gutter={[16, 36]}>
                    <ConfigProvider
                        theme={{
                            components: {
                                Statistic: {
                                    /* 这里是你的组件 token */
                                    contentFontSize: 70, // 自定义内容字体大小
                                    titleFontSize: 30, // 自定义标题字体大小
                                },
                            },
                        }}
                    >
                        <Col span={6}>
                            <Card size='small' style={{ minHeight: '20vh' }}>
                                <Statistic title="进行中任务数" value={5} suffix={<FileTextOutlined />} />
                            </Card>
                        </Col>
                        <Col span={6}>
                            <Card size='small' style={{ minHeight: '20vh' }}>
                                <Statistic title="已完成任务数" value={7} suffix={<FileDoneOutlined />} />
                            </Card>
                        </Col>
                        <Col span={6}>
                            <Card size='small' style={{ minHeight: '20vh' }}>
                                <Statistic title="待启动任务数" value={7} suffix={<FileOutlined />} />
                            </Card>
                        </Col>
                        <Col span={6}>
                            <Card size='small' style={{ minHeight: '20vh' }}>
                                <Statistic title="总任务数" value={7} suffix={<ProfileOutlined />} />
                            </Card>
                        </Col>
                    </ConfigProvider>
                    <Col span={12}>
                        <Card
                            style={{ minHeight: '70vh' }}
                            title="任务运行动态日志"
                            styles={{
                                title: {
                                    textAlign: 'center',
                                }
                            }}
                        />
                    </Col>
                </Row>
            </Card>

        </PageContainer>
    );

}


export default OverView;