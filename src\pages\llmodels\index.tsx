import { useTranslation } from 'react-i18next';
import CopyInput from '@/components/Copy/CopyInput';
import CopyBtn from '@/components/Copy/CopyBtn';
import BasicContent from '@/components/Content/BasicContent';

function LLModels() {
  const { t } = useTranslation();
  return (
    <div className="py-16px px-16px">
      <BasicContent isPermission={true}>
        <div className="py-16px px-16px">
          <h1>{t("content.clipboard")}：</h1>
          <CopyInput className="w-350px" />
          <div className="flex items-center mt-50px">
            <span className="text-lg">{t("content.clipboardMessage")}：</span>
            <CopyBtn text={t("public.copy")} value="admin" />
          </div>
        </div>
      </BasicContent>
    </div>
  );
}

export default LLModels;