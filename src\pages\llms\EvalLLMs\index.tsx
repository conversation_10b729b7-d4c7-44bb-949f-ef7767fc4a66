import BasicContent from '@/components/Content/BasicContent';
import { FilePdfFilled, FilePdfOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  FooterToolbar,
  ModalForm,
  ProCard,
  ProFormDigit,
  ProFormGroup,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Drawer, Input, message, Modal, Tabs, Popconfirm, Form } from 'antd';
import React, { useRef, useState } from 'react';
import RequestHeaderTable from './components/RequestHeaderTable';
import { addEvalLLM, getEvalllmsList, updateEvalLLM, removeEvalLLM, testLink } from '@/servers/llms';

const EvalLLMs: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, handleModalOpen] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  // const [templateDrawerVisible, setTemplateDrawerVisible] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.LLMItem>({} as API.LLMItem);
  const [selectedRowsState, setSelectedRows] = useState<API.LLMItem[]>([]);
  const [showReport, setShowReport] = useState<boolean>(false);
  const [selectedLlmType, setSelectedLlmType] = useState<string>('请选择模型');
  const [selectedTab, setSelectedTab] = useState<string>('tab1');
  const { TabPane } = Tabs;
  const [requestHeaders, setRequestHeaders] = useState([]);
  // 添加测试相关状态
  const [testLoading, setTestLoading] = useState<boolean>(false);
  const [testPassed, setTestPassed] = useState<boolean>(false);
  const [form] = Form.useForm(); // 创建表单实例

  const columns: ProColumns<API.LLMItem>[] = [
    {
      title: '序号',
      dataIndex: 'id',
      valueType: 'indexBorder',
      width: 60,
      hideInSearch: true,
    },
    {
      title: "模型ID",
      dataIndex: 'id',
      tip: 'The id is the unique key',
      hideInSearch: true,


    },
    {
      title: "模型名称",
      dataIndex: 'name',
      search: {
        transform: (value) => ({ name: value }),
      },
    },
    {
      title: "api_url",
      dataIndex: 'api_url',
      hideInTable: true,
      valueType: 'textarea',
      search: false,
    },
    {
      title: "api_key",
      dataIndex: 'api_key',
      hideInTable: true,
      valueType: 'textarea',
      search: false,

    },
    {
      title: "请求类型",
      dataIndex: 'llm_type',
      valueType: 'textarea',
      search: false
    },
    // {
    //   title: "描述",
    //   dataIndex: 'description',
    //   valueType: 'textarea',
    //   search: false
    // },
    {
      title: "创建时间",
      dataIndex: 'create_time',
      valueType: "dateTime",
      search: false
    },
    {
      title: "更新时间",
      dataIndex: 'update_time',
      hideInTable: true,
      search: false
    },
    {
      title: "操作",
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        // <a
        //   key="config"
        //   onClick={() => {
        //     // handleEditClick(record);
        //     console.log("record:",record);
        //     setCurrentRow(record);
        //     setSelectedLlmType(record?.llm_type || 'openai')
        //     handleModalOpen(true);
        //   }}
        // >
        //   编辑
        // </a>,
        <a
          key="config"
          onClick={() => {
            // console.log("record:", record);
            setCurrentRow(record);
            setSelectedLlmType(record?.llm_type || 'openai');
            setTestPassed(false); // 重置测试状态
            setTestLoading(false); // 重置加载状态
            handleModalOpen(true);

            // 延迟设置表单值，确保模态框已经打开
            setTimeout(() => {
              form.setFieldsValue(record);
            }, 100);
          }}
        >
          编辑
        </a>,
        <Popconfirm
          key="config"
          title={`确认删除模型 "${record.name}" 吗？此操作不可逆！`}
          onConfirm={() => {
            handleRemove([record]);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}
          okText="确认"
          cancelText="取消"
        >
          <a>
            删除
          </a>
        </Popconfirm>
      ],
    },
  ];

  //添加模型
  const handleAdd = async (fields: API.LLMItem) => {
    try {
      console.log("fields:", fields);
      if (fields.template_option) { fields.template_option.headers = [...requestHeaders] }
      await addEvalLLM(fields);
      message.success('添加成功');
      return true;
    } catch (error) {
      message.error('添加失败，请重试！');
      return false;
    }
  };

  //更新模型
  const handleUpdate = async (fields: any) => {
    try {
      console.log("fields:", fields);
      if (fields.template_option) { fields.template_option.headers = [...requestHeaders] }
      await updateEvalLLM(fields.id, fields);
      message.success('更新成功');
      return true;
    } catch (error) {
      message.error('更新失败，请重试！');
      return false;
    }
  };

  //删除模型
  const handleRemove = async (selectedRows: API.LLMItem[]) => {
    const hide = message.loading('正在删除');
    if (!selectedRows) return true;
    try {
      // 对于单个删除，直接使用第一个模型的ID
      if (selectedRows.length === 1) {
        await removeEvalLLM({ id: selectedRows[0].id });
      } else {
        // 批量删除时，逐个删除
        for (const row of selectedRows) {
          await removeEvalLLM({ id: row.id });
        }
      }
      hide();
      message.success('删除成功');
      return true;
    } catch (error) {
      hide();
      message.error('删除失败，请重试！');
      return false;
    }
  };

  // 定义报告点击事件
  const handleReportClick = async (record: API.LLMItem) => {
    try {
      const hide = message.loading('正在获取评估报告');
      setCurrentRow(record);
      setShowReport(true);
      // const res = await getTestResult({ id: record.id });
      hide();
      // if (res) {
      //   setCurrentRow(record);
      //   setShowReport(true);
      //   message.success('评估报告获取成功');
      // }
    } catch (error) {
      message.error('获取评估报告失败，请重试！');
    }
  };

  //定义模板选择
  const handleTemplateSelect = (value: string) => {
    setSelectedLlmType(value);
  }
  // 添加测试连接函数
  const handleTestConnection = async () => {
    try {
      setTestLoading(true);

      // 获取当前表单值
      const formValues = await form.validateFields();

      // 构建测试参数
      const testParams = {
        api_url: formValues.api_url,
        api_key: formValues.api_key,
        llm_type: formValues.llm_type,
        model_type: "evaluate",
        // 根据需要添加其他参数
        ...(formValues.internal_option && { internal_option: formValues.internal_option }),
        ...(formValues.template_option && { template_option: formValues.template_option }),
      };
      // console.log("测试连接参数:", testParams);
      // 调用测试接口
      const response = await testLink(testParams);
      // console.log("测试连接响应:", response);

      if (response.message === 'success') {
        message.success('连接测试成功');
        setTestPassed(true);
      } else {
        message.error('连接测试失败，请检查配置');
        setTestPassed(false);
      }
    } catch (error) {
      console.error('测试连接失败:', error);
      message.error('连接测试失败，请检查配置');
      setTestPassed(false);
    } finally {
      setTestLoading(false);
    }
  };

  // 修改模态框打开处理函数，重置测试状态
  const handleModalOpenChange = (open: boolean) => {
    handleModalOpen(open);
    if (!open) {
      setTestPassed(false);
      setTestLoading(false);
      setCurrentRow({} as API.LLMItem); // 关闭时清空当前行数据
      form.resetFields(); // 关闭时重置表单
      setSelectedLlmType('openai'); // 重置选择类型
    } else {
      // 打开模态框时也重置测试状态
      setTestPassed(false);
      setTestLoading(false);
    }
  };

  return (
    <div className="py-16px px-16px">
      <BasicContent isPermission={true}>
        <ProTable<API.LLMItem, API.PageParams>
          actionRef={actionRef}
          rowKey="id"
          search={{
            labelWidth: 120,
          }}
          toolBarRender={() => [
            <Button
              type="primary"
              key="primary"
              onClick={() => {
                setCurrentRow({} as API.LLMItem); // 清空当前行数据
                setRequestHeaders([]);
                setSelectedTab('tab1');
                setSelectedLlmType('openai');
                setTestPassed(false); // 重置测试状态
                setTestLoading(false); // 重置加载状态
                handleModalOpen(true);

                // 延迟设置默认值
                setTimeout(() => {
                  form.setFieldsValue({
                    llm_type: 'openai',
                    model_type: 'evaluate',
                  });
                }, 100);
              }}
            >
              <PlusOutlined />添加模型
            </Button>,
          ]}
          request={async (params, sort, filter) => {
            const newParams = {
              ...params,
              limit: params.pageSize || 10,
              offset: params.current,
              keyword: params.name || '',
              model_type: 'evaluate',
            }
            const { data, total, status } = await getEvalllmsList(newParams, sort, filter);
            if (status === 200) {
              return {
                data: data || [],
                total: total || 0,
                success: true
              };
            } else {
              message.error('获取模型列表失败');
              return {
                data: [],
                total: 0,
                success: false
              };
            }
          }}
          columns={columns}
          rowSelection={{
            onChange: (_, selectedRows) => {
              setSelectedRows(selectedRows);
            },
          }}
        />
        {selectedRowsState?.length > 0 && (
          <FooterToolbar
            extra={
              <div>
                已选择
                <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>{' '}
                项
              </div>
            }
          >
            <Button
              onClick={async () => {
                await handleRemove(selectedRowsState);
                setSelectedRows([]);
                actionRef.current?.reloadAndRest?.();
              }}
              danger
            >
              批量删除
            </Button>
          </FooterToolbar>
        )}

        <ModalForm
          // 根据 currentRow 是否存在动态设置标题
          title={currentRow?.id ? "修改模型" : "新建模型"}
          width="800px"
          open={modalOpen}
          form={form} // 使用 form 实例
          onOpenChange={handleModalOpenChange} // 打开处理函数
          // 关闭弹窗时，清空 currentRow
          modalProps={{
            destroyOnClose: true,
          }}
          initialValues={{}} // 设置为空对象
          onFinish={async (values) => {
            // 检查是否已经测试通过
            if (!testPassed) {
              message.error('请先测试连接通过后再提交');
              return false;
            }
            if (currentRow?.id) {
              // 若 currentRow 存在，执行修改操作
              await handleUpdate(values as API.LLMItem);
            } else {
              // 若 currentRow 不存在，执行新建操作
              await handleAdd(values as API.LLMItem);
            }
            handleModalOpen(false);
            setTestPassed(false); // 重置测试状态
            actionRef.current?.reload();
          }}
          submitter={{
            render: (props, doms) => {
              return (
                <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                  <Button
                    type="default"
                    loading={testLoading}
                    onClick={handleTestConnection}
                  >
                    测试连通
                  </Button>
                  <div style={{ display: 'flex', gap: '8px' }}>
                    <Button
                      onClick={() => {
                        // 关闭模态框
                        handleModalOpen(false);
                        // 重置测试状态
                        setTestPassed(false);
                        setTestLoading(false);
                        // 重置表单
                        props.form?.resetFields();
                      }}
                    >
                      取消
                    </Button>
                    <Button
                      type="primary"
                      loading={props.submitButtonProps?.loading}
                      disabled={!testPassed} // 只有测试通过后才启用确认按钮
                      onClick={props.submit}
                    >
                      确定
                    </Button>
                  </div>
                </div>
              );
            },
          }}
        >

          <ProFormGroup>
            <ProFormText
              label="模型ID"
              hidden
              width="md"
              name="id"
            />
            <ProFormText
              label="类型"
              hidden
              width="md"
              name="model_type"
              initialValue="evaluate"
            />


            <ProFormText
              label="模型名称"
              rules={[
                {
                  required: true,
                  message: "模型名称必填项",
                },
              ]}
              width="md"
              name="name"
            />
            <ProFormText
              label="模型URL"
              rules={[
                {
                  required: true,
                  message: "模型URL必填项",
                },
                {
                  pattern: /^https?:\/\/[\da-z.-]+(\.[a-z]{2,6})?(:\d{1,5})?(\/[\w.-]*)*\/?$/i,
                  message: "URL必须以http://或https://开头，请输入有效的URL地址",
                }
              ]}
              width="md"
              name="api_url"
            />
            <ProFormText
              label=" API-KEY"
              width="md"
              name="api_key"
            />
            <ProFormSelect
              label="请求类型"
              name="llm_type"
              width="md"
              initialValue={'openai'}
              options={[
                { value: 'openai', label: 'openai' },
                { value: 'ollama', label: 'ollama' },
                { value: 'custom', label: '自定义' },
              ]}
              onChange={handleTemplateSelect}
            />
          </ProFormGroup>

          {selectedLlmType === 'custom' &&
            <>
              <ProFormText
                label="参数配置"
                rules={[
                  {
                    required: true,
                    message: "参数配置必填项",
                  },
                ]}
              >
                <Tabs activeKey={selectedTab} onChange={setSelectedTab}>
                  <TabPane tab="请求头" key="tab1">
                    <RequestHeaderTable headers={currentRow?.template_option?.headers || []} setRequestHeaders={setRequestHeaders}>

                    </RequestHeaderTable>
                  </TabPane>
                  <TabPane tab="请求体" key="tab2">
                    <ProFormTextArea
                      label="请求体"
                      name={["template_option", "request_body"]}
                      fieldProps={{ rows: 6 }}
                    />

                  </TabPane>
                </Tabs>
              </ProFormText>
              <ProFormTextArea
                label="解析规则"
                name={["template_option", "response_extract_rule"]}
              // width="md"
              />
            </>
          }
          {selectedLlmType !== 'custom' &&

            <ProCard title='内部参数配置' bordered>
              <ProFormGroup>
                <ProFormText
                  label="model"
                  name={["internal_option", "model"]}
                  rules={[
                    {
                      required: true,
                      message: "模型必填项",
                    },
                  ]}
                  width="md"
                />
                <ProFormDigit
                  label="top_p"
                  name={["internal_option", "top_p"]}
                  width="md"
                  fieldProps={{
                    precision: 2, // 保留2位小数
                    step: 0.1     // 步进值0.1
                  }}
                /></ProFormGroup>
              <ProFormGroup>
                <ProFormDigit
                  label="top_k"
                  name={["internal_option", "top_k"]}
                  width="md"
                  fieldProps={{
                    precision: 2, // 保留2位小数
                    step: 0.1     // 步进值0.1
                  }}
                />
                <ProFormDigit
                  label="temperature"
                  name={["internal_option", "temperature"]}
                  width="md"
                  fieldProps={{
                    precision: 2, // 保留2位小数
                    step: 0.1     // 步进值0.1
                  }}
                />
              </ProFormGroup>
              <ProFormGroup>
                <ProFormDigit
                  label="max_tokens"
                  name={["internal_option", "max_tokens"]}
                  width="md"
                  fieldProps={{
                    precision: 0 // 整数
                  }}
                />
                <ProFormDigit
                  label="repetition_penalty"
                  name={["internal_option", "repetition_penalty"]}
                  width="md"
                  fieldProps={{
                    precision: 2, // 保留2位小数
                    step: 0.1     // 步进值0.1
                  }}
                />
              </ProFormGroup>
            </ProCard>
          }


          {/* <ProFormGroup> */}
          <ProFormTextArea
            label="描述"
            name="description"
          // maxLength={6}
          // width="md"
          />
          {/* </ProFormGroup> */}

        </ModalForm>

        <Drawer
          width={800}
          open={showReport}
          onClose={() => {
            setCurrentRow({} as API.LLMItem);
            setShowReport(false);
          }}
          closable={false}
        >
          {/* <LLMComplianceReport /> */}
        </Drawer>
      </BasicContent>
    </div>
  );
};

export default EvalLLMs;
