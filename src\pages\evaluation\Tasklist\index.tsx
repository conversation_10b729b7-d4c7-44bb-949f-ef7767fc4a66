import { getTestllmsList } from '@/servers/llms';
import { addTask, getTasksList, removeTask, retryTask, updateTask } from '@/servers/evaluation';
import { getPolicyList } from '@/servers/configuration';
import { FilePdfOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  ModalForm,
  PageContainer,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { Badge, Button, Drawer, message, Popconfirm, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import TaskExpandPanle from './components/TaskExpandPanle';
// import LLMComplianceReport from '../../LLMComplianceReport'

/**
 * @en-US Add node
 * @zh-CN 添加节点
 * @param fields
 */
const handleAdd = async (fields: API.TaskItem) => {
  try {
    
    await addTask(fields);
    message.success('创建任务成功');
    return true;
  } catch (error) {
    message.error('创建任务失败，请重试');
    return false;
  }
};

/**
 * @en-US Add node
 * @zh-CN 更新节点
 * @param fields
 */

const handleUpdate = async (fields: API.TaskItem) => {
  try {
    await updateTask(fields.id, fields);
    message.success('更新成功');
    return true;
  } catch (error) {
    message.error('更新失败，请重试');
    return false;
  }
};


/**
 *  Delete node
 * @zh-CN 删除节点
 *
 * @param selectedRows
 */
const handleRemove = async (id: string) => {
  try {
    await removeTask(id);
    message.success('删除成功');
    return true;
  } catch (error) {
    message.error('删除失败，请重试');
    return false;
  }
};


/**
 *  Delete node
 * @zh-CN 删除节点
 *
 * @param selectedRows
 */
const handleStop = async (id: string) => {
  try {
    // await stopTask(id);
    message.success('打开报告');
    return true;
  } catch (error) {
    message.error('打开报告失败，请重试');
    return false;
  }
};



const TaskList: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, handleModalOpen] = useState<boolean>(false);
  const [showReport, setShowReport] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.LLMItem>();
  const [evaluateMethod, setEvaluateMethod] = useState<string>('');
  const [modelList, setModelList] = useState<API.LLMItem[]>([]);
  const [policyList, setPolicyList] = useState<API.LLMItem[]>([]);
  const [evalModelList, setEvalModelList] = useState<API.LLMItem[]>([]);



  const initData = async () => {
    getTestllmsList({
      limit: 50,
      offset: 1,
      model_type: 'test',
    }).then((res) => {
      setModelList(res.data);
    });
    getTestllmsList({
      limit: 50,
      offset: 1,
      model_type: 'evaluate',
    }).then((res) => {
      setEvalModelList(res.data);
    });
    getPolicyList({
      limit: 50,
      offset: 1,
    }).then((res) => {
      // const defalutlist = [
      //   { value: '5a37d6fb-da07-4959-82b4-0d6398e00e51', label: 'TC260' },
      //   { value: 'ca7110e9-6bc4-43bd-a94b-22222', label: 'OWASP' }
      // ]
      // setPolicyList(defalutlist)
      if(res.data){
      const formatted = res.data?.map(item => ({
        value: item.id,
        label: item.name
      })) || [];
      setPolicyList([...formatted]);
      }
    });

  }

  useEffect(() => {
    initData()
  }, []);


  /**
 * 查看报告
 * @returns 
 */

  const handleReport = async (record: API.TaskItem) => {
    window.open(`/report/${record.id}`, '_blank');
    // try {
    //   // await stopTask(id);
    //   console.log(record);
    //   setCurrentRow(record);
    //   setShowReport(true)
    //   // 获取待测模型信息

    //   // 获取评估策略（分类）
    //   message.success('加载报告完成');
    // } catch (error) {
    //   setShowReport(false)
    //   message.error('加载报告完成失败，请重试');
    // }
  };

  const onClose = () => {
    setShowReport(false);
  };
  //重启任务
  const handleRetry = async (id: string) => {
    try {
      await retryTask(id);
      message.success('重启任务成功');
      return true;
    } catch (error) {
      message.error('重启任务失败，请重试');
      return false;
    }
  };
  

  const columns: ProColumns<API.LLMItem>[] = [
    {
      title: '序号',
      dataIndex: 'id',
      valueType: 'indexBorder',
      width: 60,
      hideInSearch: true,
    },
    {
      title: "任务ID",
      dataIndex: 'id',
      tip: 'The id is the unique key',
      search: false,

    },
    {
      title: "任务名称",
      dataIndex: 'name',
    },
    {
      title: "评估方法",
      dataIndex: 'evaluate_method',
      valueType: 'select',
      valueEnum: {
        "llm": {
          text: 'LLM',
        },
        'keyword': {
          text: '规则',
        },
      },
      render: (_, record) => (
        <Space>
          {record?.evaluate_method?.name}
        </Space>
      ),
    },
    {
      title: "状态",
      dataIndex: ["status", "status"],
      valueType: 'select',
      // search: ,
      valueEnum: {
        "completed": {
          text: '完成',
          status: 'Success',
        },
        "partial_success": {
          text: '部分成功',
          status: 'Success',
        },
        'running': {
          text: '运行中',
          status: 'Processing',
        },
        'canceled': {
          text: '取消',
          status: 'Error',
        },
        'failed': {
          text: '失败',
          status: 'Error',
        },
        '': {
          text: '待开始',
          status: 'Default',
        },
        // null: {
        //   text: '待开始',
        //   status: 'Default',
        // },
        // undefined: {
        //   text: '待开始',
        //   status: 'Default',
        // },
      },
      render: (_, record) => {
        const status = record.status?.status || record.status || '';
        const statusConfig = {
          "completed": { text: '完成', color: 'success' },
          "partial_success": { text: '部分成功', color: 'success', clickable: true },
          'running': { text: '运行中', color: 'processing' },
          'canceled': { text: '取消', color: 'error' },
          'failed': { text: '失败', color: 'error' },
          '': { text: '待开始', color: 'default' },
        };
        
        const config = statusConfig[status] || { text: '待开始', color: 'default' };
        
        const handleStatusClick = async () => {
          if (config.clickable && status === 'partial_success') {
            const success = await handleRetry(record.id);
            if (success && actionRef.current) {
              actionRef.current.reload();
            }
          }
        };
        
        return (
          <Badge 
            status={config.color} 
            text={config.text}
            style={config.clickable ? { cursor: 'pointer' } : {}}
            onClick={handleStatusClick}
          />
        );
      },
    },
    {
      // {
      //   path: '/report/:taskid',
      //   name: '报告预览',
      //   component: './LLMComplianceReport',
      // }
      title: "评估报告",
      search: false,
      render: (_, record) => {
        return <><Button type="primary" icon={<FilePdfOutlined />} shape='circle' onClick={() => handleReport(record)}></Button></>
      }

    },
    {
      title: "创建时间",
      dataIndex: 'create_time',
      valueType: 'dateTime',
      hideInSearch: true
    },
    {
      title: "操作",
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => {

        return [
          <>
            <Popconfirm
              title="删除任务"
              description="确定删除任务吗？"
              okText="确定"
              cancelText="取消"
              onConfirm={() => {
                handleRemove(record.id);
                if (actionRef.current) {
                  actionRef.current.reload();
                }
              }}
            >
              <Button type='link' danger>删除</Button>
            </Popconfirm></>
        ]
        // record?.status?.status === 'running'?
        // <>
        //   <Popconfirm
        //     title="停止该任务"
        //     description="确定停止该任务吗？"
        //     okText="确定"
        //     cancelText="取消"
        //     onConfirm={() => {
        //       handleStop(record.id);
        //       if (actionRef.current) {
        //         actionRef.current.reload();
        //       }
        //     }}
        //   >
        //     <Button type='link'>停止</Button>
        //   </Popconfirm></>
        //   :<></>


      }
    },
  ];
  // const handleEvaluateMethodChange = (value: string) => {
  //   setEvaluateMethod(value);
  // };

  return (
     <div className="py-16px px-16px">
      <ProTable<API.LLMItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              setCurrentRow(undefined)
              handleModalOpen(true);
            }}
          >
            <PlusOutlined />创建任务
          </Button>,
        ]}
        request={async (params, sort, filter) => {
          const newParams = {
            ...params,
            limit: params.pageSize || 10,
            offset: params.current ,
            keyword: params.name || '',
          }
          const { data, total, status } = await getTasksList(newParams, sort, filter);
          // if (status === 200) {
            return {
              data: data || [],
              total: total || 0,
              success: true
            };
          // } else {
          //   return {
          //     data: [],
          //     total: 0,
          //     success: false
          //   };
          // }

        }}
        pagination={{
          pageSize: 10,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,

        }}
        columns={columns}

        expandable={
          {
            expandedRowRender: (record) => {
              return (
                <TaskExpandPanle task={record} />
              );

            }
          }
        }

      />


      <ModalForm
        title={currentRow?.id ? "修改任务" : "新建任务"}
        width="500px"
        open={modalOpen}
        onOpenChange={handleModalOpen}
        initialValues={{
          ...currentRow
        }}
        // 关闭弹窗时，清空 currentRow
        modalProps={{
          destroyOnClose: true,
        }}
        onFinish={async (values) => {
          const success = values.id ? await handleUpdate(values as API.TaskItem) : await handleAdd(values as API.TaskItem);
          if (success) {
            handleModalOpen(false);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      >

        <ProFormText
          label="任务ID"
          name="id"
          hidden
        />
        <ProFormText
          label="任务名称"
          rules={[
            {
              required: true,
              message: "任务名称必填项",
            },
          ]}
          name="name"
        />
        <ProFormSelect
          label="待测模型"
          name="model_id"
          initialValue={'请选择模型'}
          options={modelList?.map((item) => ({ value: item.id, label: item.name }))||[]}
        />
        <ProFormSelect
          label="评估策略"
          name="strategy_id"
          initialValue={policyList[0]?.value}
          options={policyList}
        />
        <ProFormRadio.Group
          name={["evaluate_method", "name"]}
          label="评估方法"
          initialValue={'keyword'}
          onChange={(e) => setEvaluateMethod(e.target.value)}
          options={[
            {
              label: '规则检测',
              value: 'keyword',
            },
            {
              label: '大模型评估',
              value: 'llm',
            },
            {
              label: '自启发式',
              value: 'zero_one',
            },
          ]}
        />
        {evaluateMethod === 'llm' && <>
          <ProFormSelect
            label="评估模型"
            name={["evaluate_method", "args", "llm_id"]}
            // width="md"
            options={evalModelList.map((item) => ({ value: item.id, label: item.name }))}
          // onChange={handleEvaluateMethodChange}
          />
        </>}
        <ProFormTextArea label="任务描述" name="description" />
      </ModalForm>

    </div>
  );
};

export default TaskList;
