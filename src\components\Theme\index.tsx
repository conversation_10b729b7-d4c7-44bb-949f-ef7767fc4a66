import type { AppDispatch } from '@/stores';
import { ThemeType, setThemeValue } from '@/stores/public';
import { Dropdown, MenuProps, Space } from 'antd';
// import { Icon } from '@iconify/react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { THEME_KEY } from '@/utils/config';
import { useDispatch } from 'react-redux';
import { useAliveController } from 'react-activation';
import { SkinFilled } from '@ant-design/icons';

function Theme() {
  const { t } = useTranslation();
  const { clear, refresh, getCachingNodes } = useAliveController();
  const dispatch: AppDispatch = useDispatch();
  const themeCache = (localStorage.getItem(THEME_KEY) ||
    "default") as ThemeType;
  const [theme, setTheme] = useState<ThemeType>(themeCache);

  const items: MenuProps["items"] = [
    {
      key: "default",
      label: t("public.themeDefault"),
    },
    {
      key: "dark",
      label: t("public.themeDark"),
    },
    {
      key: "ga",
      label: t("public.themeGa"),
    },
  ];
  useEffect(() => {
   if (!themeCache) {
     localStorage.setItem(THEME_KEY, "default");
   }
   switch (themeCache) {
     case "dark":
       document.body.className = "theme-dark";
       break;

     case "ga":
       document.body.className = "theme-ga";
       break;
     default:
       break;
   }
   dispatch(setThemeValue(themeCache || "default"));
  }, [themeCache]);

  /** 刷新全部keepalive */
  const refreshAllKeepalive = () => {
    const cacheNodes = getCachingNodes();

    for (let i = 0; i < cacheNodes?.length; i++) {
      const { name } = cacheNodes[i];
      if (name) refresh(name);
    }
  };

  /**
   * 处理更新
   * @param type - 主题类型
   */
  const onChange = (type: ThemeType) => {
    localStorage.setItem(THEME_KEY, type);
    dispatch(setThemeValue(type));
    setTheme(type);

    clear();
    refreshAllKeepalive();
    switch (type) {
      case "dark":
        document.body.className = "theme-dark";
        break;
      case "ga":
        document.body.className = "theme-ga";
        break;
      default:
        document.body.className = "theme-primary";
        break;
    }
  };
  /** 点击菜单 */
  const onClick: MenuProps["onClick"] = (e) => {
    switch (e.key as ThemeType) {
      case "default":
        onChange("default");
        break;

      case "dark":
        onChange("dark");
        break;

      case "ga":
        onChange("ga");
        break;

      default:
        break;
    }
  };
  return (
    // <Tooltip title={t("public.themes")}>
    <div className="flex items-center justify-center text-lg mr-4 cursor-pointer">
      <Dropdown menu={{ items, onClick }}>
        {/* 遍历items */}
        <a onClick={(e) => e.preventDefault()}>
          <Space>
            <SkinFilled color={theme === "dark" ? "white" : "black"} />
            {/* <DownOutlined /> */}
          </Space>
        </a>
      </Dropdown>
    </div>
  );
}

export default Theme;