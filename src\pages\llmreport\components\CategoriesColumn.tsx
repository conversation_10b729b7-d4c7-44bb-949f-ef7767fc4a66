import React, { useState, useEffect } from 'react';
import { Bar } from '@ant-design/plots';

const CategoriesColumn: React.FC = (props: any) => {
  const { columnData, qualification_rate_standard } = props;
  const paletteSemanticRed = '#F4664A';
  const brandColor = '#5B8FF9';
  const config = {
    data: columnData,
    xField: 'name',
    yField: 'rate',
    meta: {
      rate: {
        alias: '通过率',
        formatter: (v: number) => `${v}%`,
      },
    },
    style: {
      fill: ({ name, rate }) => {
        console.log('value', name,rate, qualification_rate_standard);
        if (qualification_rate_standard && qualification_rate_standard.operator === 'gte') {
          if (rate < qualification_rate_standard) {
            return paletteSemanticRed;
          }
        }
        if (qualification_rate_standard && qualification_rate_standard.operator === 'lte') {
          if (rate > qualification_rate_standard) {
            return paletteSemanticRed;
          }
        }
        return brandColor;
      },
      radiusTopLeft: 10,
      radiusTopRight: 10,
    },
    // seriesField: 'rate', // 指定颜色映射的
    label: {
      text: 'rate',
      // formatter: '.1%',
      style: {
        textAlign: (d) => (d.rate > 0.008 ? 'right' : 'start'),
        fill: (d) => (d.rate > 0.008 ? '#fff' : '#000'),
        dx: (d) => (d.rate > 0.008 ? -5 : 5),
      },
    },
    height: 240,
    axis: {
      y: {
        labelFormatter: (v: number) => `${v}%`,
      },
    },
    xAxis: {
      label: {
        autoRotate: false, // 禁止自动旋转（关键）
        style: {
          rotate: 0,        // 水平展示
          textAlign: 'center',
          fill: '#8c8c8c',
        },
        formatter: (text: string) => {
          const maxLen = 2;
          return text.length > maxLen ? text.slice(0, maxLen) + '…' : text;
        },
      },
    },

  };
  return <Bar {...config} />;
};

export default CategoriesColumn;