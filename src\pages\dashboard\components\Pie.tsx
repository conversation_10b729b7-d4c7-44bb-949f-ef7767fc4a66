import type { EChartsCoreOption } from "echarts";
import { useEffect } from "react";
import { useEcharts } from "@/hooks/useEcharts";
import { useCommonStore } from "@/hooks/useCommonStore";

const option: EChartsCoreOption = {
  color: ["#5F96EA", "#6B69F2", "#F2C664", "#65789B", "#68b92e"],
  tooltip: {
    trigger: "item",
    formatter: "{a} <br/>{b}: {c} ({d}%)",
    crossStyle: {
      shadowColor: "rgba(150,150,150,0.3)",
    },
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  series: [
    {
      name: "消费能力",
      type: "pie",
      radius: ["35%", "55%"],
      center: ["50%", "51%"],
      avoidLabelOverlap: true,
      itemStyle: {
        normal: {
          borderColor: "rgba(255,255,255,0.1)",
          borderWidth: 2,
        },
      },
      label: {
        alignTo: "edge",
        minMargin: 5,
        edgeDistance: 20,
        distanceToLabelLine: 10,
        lineHeight: 20,
        formatter: function (params: { name: any; value: any }) {
          if (params.name) {
            return `{b|${params.value}}` + "{a|}" + "\n" + `{a|${params.name}}`;
          }
        },
        rich: {
          a: {
            fontSize: 14,
            padding: [-80, 0, 0, 0],
            color: "#7E7E7E",
          },
          b: {
            fontSize: 14,
            padding: [0, 0, 0, 0],
            color: "#7E7E7E",
          },
        },
      },
      labelLine: {
        normal: {
          length: 20,
          length2: 0,
          maxSurfaceAngle: 80,
          lineStyle: {
            color: "#D0D2D5",
          },
        },
      },
      data: [
        {
          name: "Websafe无法访问",
          value: "40",
        },
        {
          name: "资产结构变更",
          value: "20",
        },
        {
          name: "挖矿事件",
          value: "20",
        },
        {
          name: "紧急扫描告警",
          value: "20",
        },
        {
          name: "WAF URL黑事件",
          value: "20",
        },
      ],
      labelLayout: function (params: {
        labelRect: { x: number; width: any };
        labelLinePoints: any;
      }) {
        const isLeft = params.labelRect.x < 400 / 2;
        const points = params.labelLinePoints;
        points[2][0] = isLeft
          ? params.labelRect.x
          : params.labelRect.x + params.labelRect.width;
        return {
          labelLinePoints: points,
        };
      },
    },
  ],
};
function Pie() {
  const { permissions } = useCommonStore();
  const [echartsRef, init] = useEcharts(option);

  useEffect(() => {
    if (permissions.length) {
      setTimeout(() => {
        init();
      }, 100);
    }
  }, [init, permissions.length]);

  return (
    <div className="h-350px">
      <div ref={echartsRef} className="w-full h-full"></div>
    </div>
  );
}

export default Pie;
