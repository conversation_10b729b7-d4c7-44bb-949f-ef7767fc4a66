import { addLLMAPITemplate, removeLL<PERSON><PERSON>Template, updateLL<PERSON>PITemplate, llmapitemplates, removeModel } from '@/servers/report';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProDescriptionsItemProps } from '@ant-design/pro-components';
import {
  FooterToolbar,
  ModalForm,
  PageContainer,
  ProDescriptions,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Drawer, Input, message, theme } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
/**
 * @en-US Add node
 * @zh-CN 添加节点
 * @param fields
 */
const handleAdd = async (fields: API.LLMAPITemplateItem) => {
  const hide = message.loading('正在添加');
  try {
    await addLLMAPITemplate({ ...fields });
    hide();
    message.success('Added successfully');
    return true;
  } catch (error) {
    hide();
    message.error('Adding failed, please try again!');
    return false;
  }
};

/**
 * @en-US Update node
 * @zh-CN 更新节点
 *
 * @param fields
 */
const handleUpdate = async (fields: any) => {
  const hide = message.loading('Configuring');
  try {
    await updateLLMAPITemplate({
      name: fields.name,
      desc: fields.desc,
      key: fields.key,
    });
    hide();

    message.success('Configuration is successful');
    return true;
  } catch (error) {
    hide();
    message.error('Configuration failed, please try again!');
    return false;
  }
};

/**
 *  Delete node
 * @zh-CN 删除节点
 *
 * @param selectedRows
 */
const handleRemove = async (selectedRows: API.LLMAPITemplateItem[]) => {
  const hide = message.loading('正在删除');
  if (!selectedRows) return true;
  try {
    await removeLLMAPITemplate({
      key: selectedRows.map((row) => row.key),
    });
    hide();
    message.success('Deleted successfully and will refresh soon');
    return true;
  } catch (error) {
    hide();
    message.error('Delete failed, please try again');
    return false;
  }
};



const LLMAPITemplates: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, handleModalOpen] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);

  const actionRef = useRef<ActionType>();
  const [currentRow, setCurrentRow] = useState<API.LLMAPITemplateItem>();
  const [selectedRowsState, setSelectedRows] = useState<API.LLMAPITemplateItem[]>([]);

  
  /**
   * @en-US International configuration
   * @zh-CN 国际化配置
   * */
  // templateName: `LLM-${index}`,
  // templateConfig:  `templateConfig-${index}`,
  // templateResponseRule: `templateResponseRule-${index}`,
  // description: `description-${index}`,
  const columns: ProColumns<API.LLMAPITemplateItem>[] = [
    {
      title: '序号',
      dataIndex: 'id',
      valueType: 'indexBorder',
      width: 60,
      hideInSearch: true,
    },
    {
      title: "模板ID",
      dataIndex: 'id',
      tip: 'The id is the unique key',
      hideInSearch: true,
    },
    {
      title: "模板名称",
      dataIndex: 'name',
    },
    {
      title: "API请求配置",
      dataIndex: 'request_body',
      valueType: 'textarea',
      search: false,
    },
    {
      title: "API返回配置",
      dataIndex: 'response_extract_rule',
      valueType: 'textarea',
      search: false,
    },

    {
      title: "描述",
      dataIndex: 'description',
      valueType: 'textarea',
      search: false
    },

     {
      title: "操作",
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => [
        <a
          key="config"
          onClick={() => {
            setCurrentRow(record);
            setShowDetail(true)
          }}
        >
          查看
        </a>,
        <a
          key="config"
          onClick={() => {
            handleModalOpen(true);
            setCurrentRow(record);
          }}
        >
          停止
        </a>,
        <a
          key="config"
          onClick={() => {
            handleRemove([record]);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }}>
            删除
         </a>

      ],
    },
  ];

  function getTemplateList() {
    throw new Error('Function not implemented.');
  }
    //定义格式化函数
    const handleFormatJSON = () => {
      if (currentRow?.request_body) {
        try {
          // 解析输入的 JSON 字符串
          const parsed = JSON.parse(currentRow.request_body);
          // 格式化 JSON 字符串，缩进为 2 个空格
          const formatted = JSON.stringify(parsed, null, 2);
          // 更新 currentRow 中的 request_body
          setCurrentRow(prevRow => ({
            ...prevRow,
            request_body: formatted
          }));
        } catch (error) {
          message.error('JSON 格式错误，请检查输入内容');
        }
      }
    };
      // 监听 currentRow 的变化，打印更新后的值
  useEffect(() => {
    if (currentRow?.request_body) {
      console.log('格式化后的 request_body:', currentRow.request_body);
    }
  }, [currentRow]);

  return (
    <PageContainer title={false}>
      <ProTable<API.LLMAPITemplateItem, API.PageParams>
        headerTitle="LLM-API模板列表"
        actionRef={actionRef}
        rowKey="key"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalOpen(true);
            }}
          >
            <PlusOutlined /> <FormattedMessage id="pages.searchTable.new" defaultMessage="New" />
          </Button>,
        ]}

        request={async (data) => {
                  try {
                    const response = await llmapitemplates(data);
        
                    if (response.status === 200) {
                      return {
                        data: response.data.list || [],
                        total: response.data.total || 0,
                        success: true
                      };
                    
                    }
                    console.log('请求失败:', {
                      data: response.data.list,
                      total: response.data.total,
                      success: true
                    });
                    return { data: [], success: false };
                  } catch (error) {
                    console.error('请求失败:', error);
                    return { data: [], success: false };
                  }}}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
             已选择
              <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>{' '}
              项
              &nbsp;&nbsp;
              <span>
                合计
                {selectedRowsState.reduce((pre, item) => pre + item.callNo!, 0)}{' '}
                万
              </span>
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
            danger
          >
            批量删除
          </Button>

        </FooterToolbar>
      )}

      <ModalForm
        title="创建模板"
        width="420px"
        open={modalOpen}
        onOpenChange={handleModalOpen}
        initialValues={{
          ...currentRow
        }}
        onFinish={async (value) => {
          const success = await handleAdd(value as API.LLMAPITemplateItem);
          if (success) {
            handleModalOpen(false);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      >
        <ProFormText
          label="模板名称"
          rules={[
            {
              required: true,
              message: "模型名称必填项",
            },
          ]}
          width="md"
          name="name"
        />
        <p>API请求体</p>
        <div>
          <Button 
            type="primary" 
            onClick={handleFormatJSON}
            style={{ marginRight: 8, marginTop: 4 }}
          >
            格式化
          </Button>
          <ProFormTextArea
            
            width="md"
            name="request_body"
            value={currentRow?.request_body || ''}
            onChange={(event: { target: { value: any; }; }) => {
              const value = event.target.value;
              // setCurrentRow({ ...currentRow, request_body: value });
              setCurrentRow(prevRow => ({
                ...prevRow,
                request_body: value
              }));
              
            }}
          />


        </div>
        {/* <p>API请求体</p>
        <ProFormTextArea
          // label="API请求体"
          width="md"
          name="request_body"
          value={currentRow?.request_body || ''}
          render
        /> */}

        <ProFormTextArea
          label="API返回体"
          width="md"
          name="response_extract_rule"
        />

      </ModalForm>

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.id && (
          <ProDescriptions<API.LLMAPITemplateItem>
            column={2}
            title={currentRow?.id}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.id,
            }}
            columns={columns as ProDescriptionsItemProps<API.LLMAPITemplateItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default LLMAPITemplates;
