export default {
  currentName: "AI in Security",
  total: "total",
  date: "date",
  search: "Search",
  create: "Create",
  edit: "Edit",
  delete: "Delete",
  inputPleaseEnter: "Please enter",
  inputPleaseSelect: "Please select",
  createTitle: "Add {{title}}",
  editTitle: "Edit {{title}}",
  pleaseEnter: "Please enter {{name}}",
  pleaseSelect: "Please select {{name}}",
  confirmMessage: "Are you sure you want to {{name}}?",
  successfulOperation: "Successful operation",
  successfullyDeleted: "Successfully deleted",
  bigDataScreen: "Big Data Screen",
  fullScreen: "Full screen",
  exitFullscreen: "Exit fullscreen",
  themes: "Themes",
  changePassword: "Change Password",
  signOut: "Sign Out",
  signOutMessage: "Are you sure to log out of the system?",
  kindTips: "Kind Tips",
  reload: "Reload",
  closeTab: "Close tab",
  closeOther: "Close other",
  closeLeft: "Close left",
  closeRight: "Close right",
  confirm: "Confirm",
  cancel: "Cancel",
  operate: "operate",
  submit: "Submit",
  open: "Open",
  close: "Close",
  back: "Go back",
  ok: "OK",
  copy: "Copy",
  copySuccessfully: "Copy successfully",
  copyFailed: "Copy failed",
  maximize: "Maximize",
  exitMaximized: "Exit maximized",
  totalNum: "A total of {{num}} data",
  name: "name",
  creationTime: "Creation time",
  updateTime: "Update time",
  refreshSuccessfully: "Refresh successfully",
  notSearchContent: "There is no search content",
  switch: "Switch",
  content: "Content",
  title: "title",
  returnHome: "Return home",
  notPermissionMessage:
    "The current page cannot be accessed, it may not have permission or has been deleted!",
  notFindMessage:
    "The current page cannot be accessed, it may not have permission or has been deleted",
  requiredForm: "{{label}} is required!",
  validateEmail: "{{label}} is not a valid email!",
  validateNumber: "{{label}} is not a valid number!",
  validateRange: "{{label}} must be between {{min}} and {{max}}",
  themeDark: "theme-dark",
  themeDefault: "theme-Default",
  themeGa: "theme-Ga",
};