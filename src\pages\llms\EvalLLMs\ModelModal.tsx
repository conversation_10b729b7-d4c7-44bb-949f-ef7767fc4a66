import { ModalForm, ProFormText, ProFormTextArea, ProFormSelect } from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useRef } from 'react';
import { handleAdd, handleUpdate } from './index';
import type { API } from './index';

interface ModelModalProps {
  visible: boolean;
  onClose: (visible: boolean) => void;
  isEdit: boolean;
  initialData?: API.LLMItem;
  onSuccess?: () => void;
}

const ModelModal: React.FC<ModelModalProps> = ({
  visible,
  onClose,
  isEdit,
  initialData,
  onSuccess,
}) => {
  const actionRef = useRef<any>();

  const handleSubmit = async (value: API.LLMItem) => {
    let success = false;
    if (isEdit) {
      success = await handleUpdate(value);
    } else {
      success = await handleAdd(value);
    }
    if (success) {
      onClose(false);
      if (onSuccess) {
        onSuccess();
      }
      if (actionRef.current) {
        actionRef.current.reload();
      }
    }
  };

  return (
    <ModalForm
      title={isEdit ? '编辑模型' : '创建模型'}
      width="420px"
      open={visible}
      onOpenChange={onClose}
      initialValues={{
        ...initialData
      }}
      onFinish={handleSubmit}
    >
      <ProFormText
        label="模型ID"
        name="id"
        rules={[
          {
            required: true,
            message: "模型ID必填项",
          },
        ]}
        width="md"
      />
      <ProFormText
        label="模型名称"
        rules={[
          {
            required: true,
            message: "模型名称必填项",
          },
        ]}
        width="md"
        name="name"
      />
      <ProFormTextArea label="描述" width="md" name="description" />
      <ProFormSelect
        label="模型"
        name="mode"
        width="md"
        initialValue={'请选择模型'}
        options={[
          { value: 'internal', label: 'internal' },
          { value: 'custom', label: 'custom' }
        ]}
        onChange={(value) => {
          // 这里可添加原有的 onChange 逻辑
        }}
      />
    </ModalForm>
  );
};

export default ModelModal;
