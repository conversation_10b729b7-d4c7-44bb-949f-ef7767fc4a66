@import url('@/assets/css/default.less');

.header {
  position: fixed;
  flex-direction: column;
  top: 0;
  left: @layoutLeft;
  right: 0;
  height: @layoutTop;
  box-sizing: border-box;
  overflow: hidden;
  border-bottom: 1px solid #eee;
  z-index: 3;
  background-color: #fff;
}

.headerCloseMenu {
  left: @layoutLeftClose !important;
}

.headerDriver {
  border-bottom: 1px solid #eee;
}

.menu {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: @layoutLeft !important;
  height: 100vh;
}

.menuClose {
  width: @layoutLeftClose !important;
}

.con {
  width: calc(100% - @layoutLeft);
  min-height: calc(100vh - @layoutTop);
  box-sizing: border-box;
  position: relative;
  left: @layoutLeft;
  top: @layoutTop;
  right: 0;
  bottom: 0;
}

.conCloseMenu {
  left: @layoutLeftClose;
  width: calc(100% - @layoutLeftClose);
}

.conMaximize {
  width: 100%;
  left: 0 !important;
  top: calc(@layoutTop / 2);
}

.headerNone {
  left: 0 !important;
  height: calc(@layoutTop / 2);
}

.none {
  display: none !important;
}

.menuNone {
  width: 0 !important;
  opacity: 0 !important;
}

.conPhone {
  left: 0 !important;
}

.headerPhone {
  left: 0 !important;
}

.leftDivide {
  border-left: 1px solid #d9d9d9;
}
