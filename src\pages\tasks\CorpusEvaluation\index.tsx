import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  PageContainer,
  ProFormText,
  ProFormUploadDragger,
  ProTable,
  ProFormTextArea,
  ProFormSelect,
  ModalForm,
} from '@ant-design/pro-components';
import { Button, Descriptions, Drawer, message, Popconfirm, Tag, Progress, Space } from 'antd';
import { DownloadOutlined, FileTextOutlined, PlusOutlined } from '@ant-design/icons';
import React, { useRef, useState, useEffect } from 'react';
import { createCorpusTask, getCorpusTaskList, removeCorpusTask, downloadCorpusReport,getLexiconList } from '@/servers/tasks';
// 提取状态映射配置为常量
const statusMap = {
  "completed": { text: '完成', color: 'green' },
  "partial_success": { text: '部分成功', color: 'green' },
  'running': { text: '运行中', color: 'blue' },
  'processing': { text: '处理中', color: 'blue' },
  'pending': { text: '待开始', color: 'gold' },
  'canceled': { text: '取消', color: 'red' },
  'failed': { text: '失败', color: 'red' },
  '': { text: '待开始', color: 'gold' },
};
//删除任务
const handleRemove = async (id: string) => {
  try {
    await removeCorpusTask(id);
    message.success('删除成功');
    return true;
  } catch (error) {
    message.error('删除失败，请重试');
    return false;
  }
};

//下载报告
const handleDownloadReport = async (id: string, taskName: string) => {
  try {
    message.loading('正在下载报告...', 0);
    const response = await downloadCorpusReport(id);
    
    // 创建下载链接
    const blob = new Blob([response.data], { type: 'application/octet-stream' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    // 从响应头获取文件名，如果没有则使用默认名称
    const contentDisposition = response.headers?.['content-disposition'];
    let filename = `${taskName}_敏感词检测报告.pdf`;
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '');
      }
    }
    
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    message.destroy();
    message.success('报告下载成功');
  } catch (error) {
    message.destroy();
    message.error('报告下载失败，请重试');
    console.error('下载报告失败:', error);
  }
};

//添加敏感词任务
const handleAdd = async (values: Record<string, any>, setUploading: (value: boolean) => void, setUploadProgress: (value: number) => void) => {
  try {
    setUploading(true);
    setUploadProgress(0);
    
    const formData = new FormData();
    if (values.name) {
      formData.append('name', values.name);
    }
    formData.append('description', values.description || '');
    formData.append('dict_id', values.dictId);
    
    // 添加文件
    values.files?.forEach((fileInfo: any) => {
      if (fileInfo.originFileObj) {
        formData.append('file', fileInfo.originFileObj);
      }
    });

    console.log('创建敏感词任务，FormData:', formData);
    
    await createCorpusTask(formData, (progressEvent: any) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      setUploadProgress(percentCompleted);
    });
    
    setUploading(false);
    message.success('添加成功');
    return true;
  } catch (error) {
    setUploading(false);
    setUploadProgress(0);
    message.error('添加失败，请重试！');
    return false;
  }
};


const CorpusEvaluation: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [drawerVisible, setDrawerVisible] = useState(false); // 控制抽屉显示隐藏
  const [currentTask, setCurrentTask] = useState<API.CorpusTaskItem | null>(null); // 存储当前任务记录
  const [modalOpen, setModalOpen] = useState<boolean>(false); // 控制表单弹窗
  const [lexiconList, setLexiconList] = useState<{ label: string; value: string }[]>([]); // 词库列表
  const [uploading, setUploading] = useState<boolean>(false); // 上传状态
  const [uploadProgress, setUploadProgress] = useState<number>(0); // 上传进度

  // 加载词库列表
  useEffect(() => {
    const loadLexicons = async () => {
      try {
        const response = await getLexiconList({
          limit: 100,
          offset: 0,
          keyword: '',
        });
        if (response && response.data) {
          const options = response.data.map((item: any) => ({
            label: item.name,
            value: item.id,
          }));
          setLexiconList(options);
        }
      } catch (error) {
        console.error('加载词库列表失败:', error);
        message.error('加载词库列表失败');
      }
    };
    loadLexicons();
  }, []);

  const columns: ProColumns<API.CorpusTaskItem>[] = [
    {
      title: '序号',
      dataIndex: 'id',
      valueType: 'indexBorder',
      width: 60,
      hideInSearch: true,
    },
    {
      title: "任务ID",
      dataIndex: 'id',
      search: false,
    },
    {
      title: "任务名称",
      dataIndex: 'name',
    },
    // {
    //   title: "文件名称",
    //   dataIndex: 'file_name',
    // },
    // {
    //   title: "文件大小",
    //   dataIndex: 'filesize',
    // },
    {
      title: "文件数量",
      dataIndex: 'files',
      // align: 'center',
      render: (files) => files && Array.isArray(files) ? files.length : 0,
    },
    {
      title: "状态",
      dataIndex: ["status"],
      valueType: 'select',
      valueEnum: {
        "completed": {
          text: '完成',
          status: 'Success',
        },
        "partial_success": {
          text: '部分成功',
          status: 'Success',
        },
        'running': {
          text: '运行中',
          status: 'Processing',
        },
        'canceled': {
          text: '取消',
          status: 'Error',
        },
        'failed': {
          text: '失败',
          status: 'Error',
        },
        '': {
          text: '待开始',
          status: 'Default',
        },
      }
    },
    // {
    //   title: "评估报告",
    //   search: false,
    //   render: (_, record) => {
    //     return <><Button type="primary" icon={<FilePdfOutlined />} shape='circle' onClick={() => handleReport(record)}></Button></>
    //   }

    // },
    {
      title: "创建时间",
      dataIndex: 'created_at',
      valueType: 'dateTime',
      hideInSearch: true
    },
    {
      title: "更新时间",
      dataIndex: 'updated_at',
      valueType: 'dateTime',
      hideInSearch: true
    },
    {
      title: "操作",
      dataIndex: 'option',
      valueType: 'option',
      render: (_, record) => {

        return [
          <Button
            key="view"
            type="link"
            onClick={() => {
              setCurrentTask(record); // 设置当前任务记录
              setDrawerVisible(true); // 打开抽屉
            }}
          >
            查看
          </Button>,
          <Popconfirm
            key="delete"
            title="删除任务"
            description="确定删除任务吗？"
            okText="确定"
            cancelText="取消"
            onConfirm={() => {
              handleRemove(record.id);
              if (actionRef.current) {
                actionRef.current.reload();
              }
            }}
          >
            <Button type='link' danger>删除</Button>
          </Popconfirm>,
        ]

      }
    },
  ];

  return (
    <div className="py-16px px-16px">
      <ProTable
        headerTitle="敏感词检测任务"
        actionRef={actionRef}
        rowKey="id"
        search={false}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setModalOpen(true);
            }}
          >
            新建任务
          </Button>,
        ]}
        pagination={{
          pageSize: 10,
          showTotal: (total, range) => `${range[0]}-${range[1]} of ${total}`,
        }}
        request={async (params, sort, filter) => {
          const newParams: any = {
            limit: params.pageSize || 10,
            offset: (params.current || 1) - 1,
          }
          
          // 只有当keyword存在时才添加到参数中
          if (params.keyword) {
            newParams.keyword = params.keyword;
          }
          
          const { data, total, status } = await getCorpusTaskList(newParams, sort, filter);
          if (status === 200) {
            return {
              data: data || [],
              success: true,
              total
            }
          }
          return {
            data: [],
            success: false,
            total: 0
          }
        }}
        columns={columns}
      />

      <ModalForm
        title="新建敏感词检测任务"
        width="600px"
        open={modalOpen}
        onOpenChange={setModalOpen}
        modalProps={{
          destroyOnClose: true,
        }}
        submitter={{
          submitButtonProps: {
            loading: uploading,
            disabled: uploading,
          },
          resetButtonProps: {
            disabled: uploading,
          },
        }}
        onFinish={async (values) => {
          const success = await handleAdd(values, setUploading, setUploadProgress);
          if (success) {
            setModalOpen(false);
            setUploadProgress(0);
            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      >
        <ProFormText
          label="任务名称"
          name="name"
          placeholder="请输入任务名称"
          rules={[{ required: true, message: '请输入任务名称' }]}
        />
        
        <ProFormTextArea
          label="任务描述"
          name="description"
          placeholder="请输入任务描述"
          fieldProps={{
            rows: 3,
          }}
        />
        
        <ProFormSelect
          label="敏感词库"
          name="dictId"
          placeholder="请选择敏感词库"
          options={lexiconList}
          rules={[{ required: true, message: '请选择敏感词库' }]}
        />
        
        <ProFormUploadDragger
          label="上传文件"
          name="files"
          description="支持 .txt, .csv, .json 格式文件，可多选"
          fieldProps={{
            accept: '.txt,.csv,.json',
            multiple: true,
            beforeUpload: () => false, // 阻止自动上传
          }}
          rules={[{ required: true, message: '请上传至少一个文件' }]}
        />
        
        {uploading && (
          <div style={{ marginTop: 16 }}>
            <div style={{ marginBottom: 8, color: '#666' }}>
              正在上传文件，请稍候...
            </div>
            <Progress 
              percent={uploadProgress} 
              status={uploadProgress === 100 ? 'success' : 'active'}
              format={percent => `${percent}%`}
            />
          </div>
        )}
      </ModalForm>

      <Drawer
        title="任务详情"
        width={800}
        open={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        bodyStyle={{ paddingBottom: 80 }}
        extra={
          currentTask && currentTask.status === 'completed' && (
            <Space>
              <Button 
                type="primary" 
                icon={<DownloadOutlined />}
                onClick={() => handleDownloadReport(currentTask.id, currentTask.name)}
              >
                下载报告
              </Button>
            </Space>
          )
        }
      >
        {currentTask && (
          <>
            <Descriptions bordered column={1} style={{ marginBottom: 16 }}>
              <Descriptions.Item label="任务ID">{currentTask.id}</Descriptions.Item>
              <Descriptions.Item label="任务名称">{currentTask.name}</Descriptions.Item>
              <Descriptions.Item label="任务描述">
                {currentTask.description || '无描述'}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={statusMap[currentTask.status]?.color || statusMap[''].color}>
                  {statusMap[currentTask.status]?.text || statusMap[''].text}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="文件数量">
                {currentTask.files && Array.isArray(currentTask.files) ? currentTask.files.length : 0} 个文件
              </Descriptions.Item>
              <Descriptions.Item label="词库ID">
                {currentTask.dict_id || '未指定'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {currentTask.created_at}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {currentTask.updated_at}
              </Descriptions.Item>
            </Descriptions>

            {/* 文件列表 */}
            {currentTask.files && Array.isArray(currentTask.files) && currentTask.files.length > 0 && (
              <div style={{ marginBottom: 16 }}>
                <h4>上传文件列表</h4>
                <div style={{ maxHeight: 200, overflowY: 'auto', border: '1px solid #f0f0f0', borderRadius: 4, padding: 8 }}>
                  {currentTask.files.map((file: any, index: number) => (
                    <div key={index} style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <FileTextOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                          <span>{file.name || file.file_name || `文件${index + 1}`}</span>
                        </div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          {file.size ? `${(file.size / 1024).toFixed(2)} KB` : ''}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </Drawer>
    </div>
  );
};

export default CorpusEvaluation;
