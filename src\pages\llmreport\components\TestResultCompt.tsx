import { useEffect, useState } from 'react';
import {
    Typography,
    Space,
    Tabs,
    Button,
    Badge,
    Row,
    Radio,
    RadioChangeEvent
} from 'antd';
import { ProList } from '@ant-design/pro-components';
import type { Key, ReactNode } from 'react';
import { getTaskReportSummaryDetail, getMissionList } from "@/services/LLMComplianceReport/api";
import PassTag from './PassTag';
import { CaretRightOutlined, DoubleRightOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import PassingRateStatisticCompt from './PassingRateStatisticCompt';
// import ReactMarkdown from 'react-markdown';
// import RemarkGfm from 'remark-gfm';
const { Paragraph, Text } = Typography;

const FoldableText = ({ text, expandstate, maxLength }) => {
    // console.log('expandstate:',expandstate)
    //   if (expandstate || text.length <= maxLength) {
    return <div style={{ backgroundColor: 'azure', padding: '30px 8px 8px 8px' }}>{text}
        {/* <ReactMarkdown  remarkPlugins={[RemarkGfm]}>{text}</ReactMarkdown> */}
    </div>;
    //   }
    //   return <span>{text.slice(0, maxLength)}...</span>;
    //   return <div style={{backgroundColor:'azure'}}><ReactMarkdown  remarkPlugins={[RemarkGfm]}>{text.slice(0, maxLength)}...</ReactMarkdown></div>;
};

const TitleFoldableText = ({ text, expandstate, maxLength }) => {
    // console.log('expandstate:',expandstate)
    if (expandstate || text.length <= maxLength) {
        return <span>{text}</span>;
    }
    return <span>{text.slice(0, maxLength)}...</span>;
    //   return <div style={{backgroundColor:'azure'}}><ReactMarkdown  remarkPlugins={[RemarkGfm]}>{text.slice(0, maxLength)}...</ReactMarkdown></div>;
};



const TestResultCompt = (props: any) => {

    const { classification, parentClassification, taskID } = props
    const [expandedRowKeys, setExpandedRowKeys] = useState<readonly Key[]>([]);
    const [prompts, setPrompts] = useState([]);
    const [dataTotal, setDataTotal] = useState(0);
    // let sub1 = Object.values(classification.children)[0];
    // let sub2 = sub1.children ? Object.values(sub1.children)[0] : ''
    const [selectTabs, setSelectTabs] = useState();
    const [filterPass, setFilterPass] = useState<number>(0); // 新增状态，记录过滤条件

    //初始化数据

    const onChange = (key: any) => {
        console.log("当前key:", key);
        // const { data, total } = await getMissionList(
        //     {
        //         task_id: taskID,
        //         classification: category?.name,
        //         offset: current,
        //         limit: pageSize,
        //     }
        // )

        setSelectTabs(key);
    }
    const preStyle = {
        whiteSpace: 'pre-wrap', wordBreak: 'break-all',
        fontFamily: 'PingFang SC, PingFang SC-Regular',
        fontWeight: '400',
        color: '#535353',
        border: 'none',
    };

    const onAICompliance = () => {
        const llm = ""
    }
    const isEmptyObj = (obj: any) => {
        const isEmpty = Object.keys(obj).length === 0;
        return isEmpty;
    }
    const onChangeRadio = async (e: RadioChangeEvent) => {
        setFilterPass(e.target.value); // 更新过滤条件
        setExpandedRowKeys([]); 
    };




    // console.log("TestResultCompt    parentName:",parentName,"   model:",model)
    return (
        <>

            <Tabs type="card" style={{ marginTop: '10px' }} key={parentClassification?.id + classification?.id} onChange={onChange}>
                {
                    classification.children && Object.values(classification.children).map((category, prindex) => {
                        console.log("category:", category);
                        return <Tabs.TabPane
                            tab={category.name_alias || category.name}
                            key={parentClassification?.id + classification?.id + category.id + prindex}
                        >
                            {
                                category.description && <pre style={preStyle}>{category.description}</pre>
                            }
                            {
                                category.detection_principle && <li>
                                    <Text strong>检测方法</Text>
                                    <blockquote>{category.detection_principle}</blockquote>
                                </li>
                            }
                            <li>
                                <Text strong>检测结果</Text>
                                {
                                    category.show_summary && <>
                                        <PassingRateStatisticCompt
                                            show_categories_column={category.show_categories_column}
                                            classification={category}
                                            taskID={taskID}
                                            subclass={category.children}
                                            qualification_rate_standard={isEmptyObj(category.qualification_rate_standard) ? parentClassification?.qualification_rate_standard : classification.qualification_rate_standard}
                                        />

                                    </>
                                }
                            </li>

                            {
                                category.children &&
                                <ProList
                                    headerTitle={
                                        <Space justify="flex-end" style={{ width: '100%' }}>
                                            <Text>分类详情展示（ 未通过 / 总数 ）</Text>
                                            <Radio.Group defaultValue={0} size="small"onChange={onChangeRadio}>
                                                <Radio.Button value={0}>全部</Radio.Button>
                                                <Radio.Button value={1}>通过</Radio.Button>
                                                <Radio.Button value={2}>未通过</Radio.Button>
                                            </Radio.Group>
                                        </Space>
                                    }
                                    rowKey="task_id"
                                    showActions="hover"
                                    key={'prolist--' + parentClassification?.id + classification?.id + category.id}
                                    request={async (params = {}) => {
                                        const { pageSize, current } = params
                                        // console.log("当前分类查询:", category.name, "当前页码:", current, "每页条数:", pageSize);
                                        const { data, total } = await getTaskReportSummaryDetail(
                                            {
                                                task_id: taskID,
                                                classification_id: category.id,
                                                offset: current,
                                                limit: pageSize,
                                              
                                            }
                                        )

                                        // console.log("加载的数据外层prolist：", data);

                                        // setPrompts(data);
                                        // setDataTotal(total);
                                        return {
                                            data,
                                            success: true,
                                            total,
                                        };
                                    }}
                                    expandable={{
                                        expandedRowKeys,
                                        onExpandedRowsChange: setExpandedRowKeys,
                                        expandIcon: ({ expanded }: { expanded: boolean }) => (
                                            <CaretRightOutlined style={{ transform: expanded ? 'rotate(0deg)' : 'rotate(0deg)' }} />
                                        )
                                    }}
                                    metas={
                                        {
                                            title: {
                                                dataIndex: 'name',
                                                render: (text: ReactNode, row: any) => (
                                                    <Space size={2}>
                                                        <span style={{ fontSize: 14 }}>{text}</span>
                                                        {/* <span style={{ fontSize: 14, color: 'gray' }}>({row.missions_count})</span> */}
                                                        <Text type="secondary" style={{ fontSize: 14 }}>( {(row.missions_count - row.pass_count) + ' / ' + row.missions_count} )</Text>
                                                    </Space>
                                                ),
                                            },
                                            description: {
                                                render: (text, row) => {

                                                    const currentItemId = row.id;

                                                    return (
                                                        <div>
                                                            <ProList
                                                                key={`${currentItemId}-${selectTabs}${taskID}`}
                                                                rowKey="id"
                                                                showActions="hover"
                                                                pagination={{
                                                                    pageSize: 10,
                                                                    showSizeChanger: false,
                                                                    total: dataTotal
                                                                }}
                                                                expandable={{
                                                                    expandedRowKeys,
                                                                    onExpandedRowsChange: setExpandedRowKeys,
                                                                    expandIcon: ({ expanded }: { expanded: boolean }) => (
                                                                        <DoubleRightOutlined style={{ transform: expanded ? 'rotate(0deg)' : 'rotate(0deg)' }} />
                                                                    )
                                                                   
                                                                }}
                                                                request={async (params = {}) => {
                                                                    const { pageSize, current } = params
                                                                    const { data, total } = await getMissionList(
                                                                        {
                                                                            task_id: taskID,
                                                                            classification_id: currentItemId,
                                                                            offset: current,
                                                                            limit: pageSize,
                                                                            pass: filterPass || 0, // 使用过滤条件
                                                                        }
                                                                    )
                                                                    setDataTotal(total)
                                                                    return {
                                                                        data,
                                                                        success: true,
                                                                        total,
                                                                    };
                                                                }}

                                                                metas={{
                                                                    title: {
                                                                        render: (text, row, index, action) => (

                                                                            <Space size={2}>
                                                                                <span style={{ fontSize: 14, color: 'gray' }}>提问：</span>
                                                                                <span style={{ fontSize: 14 }}>
                                                                                    <TitleFoldableText text={row?.raw_data?.[0]?.request || ""} maxLength={50} expandstate={expandedRowKeys.includes(row.id)} />
                                                                                </span>
                                                                            </Space>
                                                                        ),
                                                                    },
                                                                    subTitle: {
                                                                        render: (text, row, index, action) => (
                                                                            <Space size={2}>
                                                                                <PassTag score={row?.evaluate_result?.pass} />
                                                                                {/* {expandedRowKeys.includes(row.id) && (
                                                                                    <Button size="small">AI</Button>
                                                                                )} */}
                                                                            </Space>
                                                                        ),
                                                                    },
                                                                    // avatar: {
                                                                    //     render: () => <QuestionCircleOutlined />,
                                                                    // },
                                                                    description: {
                                                                        render: (text, row, index, action) => (
                                                                            <Paragraph trigger="hover">
                                                                                {/* <Badge.Ribbon text="回复" color='orange' placement="start" />
                                                                                <FoldableText text={row?.raw_data?.[0]?.response || ""} maxLength={700} expandstate={true} />
                                                                                <Badge.Ribbon text="根因" color='blue' placement="start" />
                                                                                <pre style={{ fontSize: '14px', fontFamily: 'Courier New', padding: '16px 8px 8px 8px' }}>
                                                                                    {row?.evaluate_result?.extra_data?.reason}
                                                                                </pre> */}
                                                                                <Text style={{ color: '#7e7e7e' }}>回复：</Text>
                                                                                <span>{row?.raw_data?.[0]?.response || ""}</span>
                                                                                <br />
                                                                                <Text style={{ color: '#7e7e7e' }}>根因：</Text>
                                                                                <span>{row?.evaluate_result?.extra_data?.reason}</span>


                                                                            </Paragraph>
                                                                        ),
                                                                    },
                                                                }}
                                                            />
                                                        </div>
                                                    )
                                                },
                                            }

                                        }
                                    }

                                >

                                </ProList>
                            }
                        </Tabs.TabPane>


                    })
                }

            </Tabs >

            {


                classification.children === undefined && <ProList
                    //设置prompt_id + scan_at的字符串拼接为key
                    rowKey={(row, index) => { return row.id + '-' + index }}
                    // dataSource={prompts}
                    showActions="hover"
                    key={'prolist--' + parentClassification?.id + classification?.id}
                    request={async (params = {}) => {
                        const { pageSize, current } = params
                        console.log("当前分类查询:", classification?.name, "当前页码:", current, "每页条数:", pageSize);
                        const { data, total } = await getMissionList(
                            {
                                task_id: taskID,
                                // classification: category?.name,
                                classification_id: classification?.id,
                                offset: current,
                                limit: pageSize,
                            }
                        )

                        // console.log("加载的数据：", data);

                        // setPrompts(data);
                        // setDataTotal(total);
                        return {
                            data,
                            success: true,
                            total,
                        };


                    }}
                    pagination={{
                        pageSize: 15,
                        showSizeChanger: false,
                        total: dataTotal
                    }}
                    expandable={{
                        expandedRowKeys,
                        onExpandedRowsChange: (key: any) => {
                            setExpandedRowKeys(key);
                            // console.log(key);
                        },
                        // expandIcon: ({ expanded }: { expanded: boolean }) => (
                        //     <CaretRightOutlined style={{ transform: expanded ? 'rotate(0deg)' : 'rotate(0deg)' }} />
                        // )
                    }}
                    metas={{
                        title: {
                            render: (text, row, index, action) => {
                                // console.log(row.key);
                                return (
                                    <Space size={2}>
                                        <span style={{ fontSize: 14 }}>
                                            {/* 通过prompt_id和scan_at的凭借字符串判断当前行是否为点击行，若是，则展开改行标题所有内容 */}
                                            <TitleFoldableText text={row?.raw_data?.[0]?.request || ""} maxLength={65} expandstate={expandedRowKeys.includes(row.id + '-' + index)} />
                                            {/* {row.prompt} */}
                                        </span>
                                    </Space>
                                );
                            },
                        },

                        subTitle: {
                            render: (text, row, index, action) => {
                                return (
                                    <Space size={2}>
                                        <PassTag score={row?.evaluate_result?.pass} />
                                        {expandedRowKeys.includes(row.id + '-' + index) && (
                                            <Button type="primary" shape="round" size="small">AI</Button>
                                        )}
                                    </Space>
                                );
                            },
                        },
                        avatar: {
                            render: () => {
                                return <QuestionCircleOutlined />;
                                // return <>avatar</>
                            },
                        },
                        description: {
                            render: (text, row, index, action) => {
                                return (
                                    <>
                                        <Paragraph>
                                            {/* 解释信息全展开 */}
                                            <Badge.Ribbon text="回复" color='orange' placement="start"></Badge.Ribbon>
                                            <FoldableText text={row?.raw_data?.[0]?.response} maxLength={600} expandstate={true} />
                                            <Badge.Ribbon text="根因" color='blue' placement="start"></Badge.Ribbon>
                                            <pre style={{ fontSize: '14px', fontFamily: 'Courier New', padding: '16px 8px 8px 8px' }}>
                                                {row?.evaluate_result?.extra_data?.reason}
                                            </pre>
                                        </Paragraph>
                                    </>
                                );
                            },
                        },
                    }}
                />

            }
        </>
    )
}

export default TestResultCompt;

