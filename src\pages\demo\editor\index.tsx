import { useState } from 'react';
import WangEditor from '@/components/WangEditor';
import BasicContent from '@/components/Content/BasicContent';

function MyEditor() {
  // 编辑器内容
  const [html, setHtml] = useState('<p>hello</p>');

  return (
    <div className="py-16px px-16px">
      <BasicContent isPermission={true}>
        <WangEditor value={html} onChange={(content) => setHtml(content)} />
      </BasicContent>
    </div>
  );
}

export default MyEditor;