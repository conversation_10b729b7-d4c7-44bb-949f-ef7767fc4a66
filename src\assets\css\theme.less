@import url("./default.less");

/**
 * 修改主题
 * @param color - 字体颜色
 * @param bgColor - 背景颜色
 * @param layoutContentBg - layout内容背景色
 * @param contentBgColor - 内容背景颜色
 * @param svgColor - svg颜色



 
 * @param memuBg - 菜单的背景色
 * @param loginBg - 登录框的背景色
 * @param loginBorder - 登录框文字的描边
 */

.changeTheme(
  @colorText: @colorText,
  @bgColor: @primaryBg,
  @layoutContentBg: @layoutContentBg,
  @contentBgColor: @contentBgColor,
  @svgColor: @svgColor,
  @memuBg:@memuBg,
  @loginBg:@loginBg,
  @loginBorder:@loginBorder,
  @colorBorder: @colorBorder,
) {
  color: @colorText !important;
  background-color: @bgColor !important;

  .bg {
    background-color: @bgColor !important;
  }

  .change svg {
    color: @svgColor !important;
  }

  a,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: @colorText !important;
  }

  #layoutContent {
    background-color: @layoutContentBg !important;
  }

  #content {
    background-color: @contentBgColor!important;
  }
  #header,
  #layout {
    color: @colorText !important;
    background-color: @bgColor !important;
  }
  #menu {
    background-color: @memuBg;
  }
  // 卡片样式
  .card-title {
    color: @colorText;
    // line-height: 56px;
  }
  .login-body {
    background: @loginBg!important;
    .text-info {
      border-top: 1px solid @loginBorder;
      border-bottom: 1px solid @loginBorder;
      color: @colorText;
    }
  }
  .border-bottom {
    border-bottom: 1px solid @colorBorder !important;
  }
}
