import { Row, Col } from 'antd';
import { Icon } from '@iconify/react';
import { useTranslation } from 'react-i18next';
import Count from '@/components/Count';

function Block() {
  const { t } = useTranslation();

  const data = [
    {
      title: t("dashboard.usersNumber"),
      num: 1966,
      all: 16236,
      icon: "icon-javascript",
    },
    {
      title: t("dashboard.rechargeAmount"),
      num: 486,
      all: 6142,
      icon: "icon-shoppingcart",
    },
    {
      title: t("dashboard.orderNumber"),
      num: 549,
      all: 5232,
      icon: "icon-java",
    },
    {
      title: t("dashboard.gameNumber"),
      num: 619,
      all: 2132,
      icon: "icon-python",
    },
  ];

  return (
    <Row>
      {data.map((item) => (
        <Col key={item.title} span={6}>
          <div className="px-30px py-20px">
            <div className="text-18px font-bold">{item.title}</div>
            <div className="flex items-center text-36px ">
              <Count className="font-bold" start={0} end={item.all} />
              <span className="text-16px ml-12px mt-10px">
                {t("public.create")}:
                <Count start={0} end={item.num} />
              </span>
              {/* <IconFont type={item.icon} /> */}
            </div>
          </div>
        </Col>
      ))}
    </Row>
  );
}

export default Block;