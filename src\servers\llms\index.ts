/**
 * 大模型测评服务 - LLM模型管理API接口
 *
 * 本文件包含了大语言模型管理相关的API接口定义，主要包括：
 * - 测试模型管理接口（用于安全测试的目标模型）
 * - 评估模型管理接口（用于评估测试结果的评估模型）
 * - 模型连通性测试接口
 * - 模型详情查询接口
 *
 * <AUTHOR>
 * @version 5.0
 * @since 2024
 */

import { request } from '@/servers/request';

/** API基础路径 */
const API_BASE = "/api";

/**
 * 通用查询参数接口
 * 用于分页查询和关键词搜索
 */
interface QueryParams {
  /** 每页显示数量 */
  limit: number,
  /** 偏移量，用于分页 */
  offset: number,
  /** 搜索关键词 */
  keyword: string,
}


/* ==================== 测试模型管理相关接口 ==================== */

/**
 * 获取测试模型列表
 * 获取用于安全测试的目标LLM模型列表，支持分页、排序和筛选
 *
 * @param params - 查询参数（分页和关键词）
 * @param sort - 排序配置对象
 * @param filter - 筛选条件对象
 * @returns Promise<API.LLMList> 返回测试模型列表数据
 */
export function getTestllmsList(
  params: QueryParams,
  sort: Record<string, any>,
  filter: Record<string, any>,
) {
  return request.post<API.LLMList>(API_BASE + '/model/list',
    {
      ...params,
      ...(sort || {}),
      ...(filter || {}),
    }
  );
}

/**
 * 更新测试模型
 * 更新现有测试模型的配置信息
 *
 * @param id - 模型唯一标识符
 * @param data - 要更新的模型数据
 * @returns Promise<API.LLMList> 返回更新结果
 */
export function updateTestLLM(id: string, data: any) {
  return request.put<API.LLMList>(API_BASE + `/model/${id}`, data
  );
}

/**
 * 新建测试模型
 * 创建新的测试模型配置
 *
 * @param data - 模型配置数据
 * @returns Promise<API.LLMList> 返回创建结果
 */
export function addTestLLM(
  data: API.LLMItem
) {
  return request.post<API.LLMList>(API_BASE + '/model/create',
    data
  );
}

/**
 * 删除测试模型
 * 根据模型ID删除指定的测试模型配置
 *
 * @param path - 路径参数
 * @param path.id - 要删除的模型ID
 * @returns Promise<API.LLMList> 返回删除操作结果
 */
export function removeTestLLM(path: { id: string }) {
  const url = API_BASE + `/model/${path.id}`;
  return request.delete<API.LLMList>(url);
}

/**
 * 获取测试评估报告
 * 获取指定模型的测试评估结果报告
 *
 * @param options - 参数对象
 * @param options.id - 模型ID
 * @returns Promise<Record<string, any>> 返回评估报告数据
 */
export function getTestResult(options: { id: string }) {
  const url = API_BASE + `/model/${options.id}`;
  return request.post<Record<string, any>>(url);
}

/**
 * 获取测试模型详情
 * 根据模型ID获取具体的测试模型配置信息
 *
 * @param path - 路径参数
 * @param path.id - 模型唯一标识符
 * @returns Promise<API.LLMList> 返回模型详细信息
 */
export function getTestllms(
  path: { id: string },
) {
  console.log("model_id", path.id);

  const url = API_BASE + `/model/${path.id}`;
  return request.get<API.LLMList>(url);
}

/**
 * 测试模型连接
 * 测试指定模型配置的网络连接和API可用性
 *
 * @param data - 模型配置数据
 * @returns Promise<API.LLMList> 返回连通性测试结果
 */
export function testLink(
  data: API.LLMItem
) {
  return request.post<API.LLMList>(API_BASE + '/model/test', data
  );
}



/* ==================== 评估模型管理相关接口 ==================== */

/**
 * 获取评估模型列表
 * 获取用于评估测试结果的LLM模型列表，支持分页、排序和筛选
 *
 * @param params - 查询参数（分页和关键词）
 * @param sort - 排序配置对象
 * @param filter - 筛选条件对象
 * @returns Promise<API.LLMList> 返回评估模型列表数据
 */
export function getEvalllmsList(
  params: QueryParams,
  sort: Record<string, any>,
  filter: Record<string, any>,
) {
  const queryData = {
      ...params,
      ...(sort || {}),
      ...(filter || {}),
    }
  return request.post<API.LLMList>(API_BASE + '/model/list',
    queryData
  );
}

/**
 * 更新评估模型
 * 更新现有评估模型的配置信息
 *
 * @param id - 模型唯一标识符
 * @param data - 要更新的模型数据
 * @returns Promise<API.LLMList> 返回更新结果
 */
export function updateEvalLLM(id: string, data: any) {
  return request.put<API.LLMList>(API_BASE + `/model/${id}`, data
  );
}

/**
 * 新建评估模型
 * 创建新的评估模型配置，用于评估测试结果
 *
 * @param data - 评估模型配置数据
 * @param data.api_key - 可选，API密钥
 * @param data.api_url - API接口地址
 * @param data.description - 模型描述
 * @param data.internal_option - 内部配置选项
 * @param data.internal_option.max_tokens - 可选，最大令牌数
 * @param data.internal_option.model - 模型名称
 * @param data.internal_option.repetition_penalty - 可选，重复惩罚系数
 * @param data.internal_option.temperature - 可选，温度参数
 * @param data.internal_option.top_k - 可选，Top-K采样参数
 * @param data.internal_option.top_p - 可选，Top-P采样参数
 * @param data.llm_type - LLM类型
 * @param data.model_type - 模型类型，固定为'evaluate'
 * @param data.name - 模型名称
 * @param data.template_option - 模板配置选项
 * @param data.template_option.headers - 请求头配置数组
 * @param data.template_option.request_body - 请求体模板
 * @param data.template_option.response_extract_rule - 响应提取规则
 * @returns Promise<API.LLMList> 返回创建结果
 */
export function addEvalLLM(data: {
  api_key?: string | null;
  api_url: string;
  description: string;
  internal_option: {
    max_tokens?: number | null;
    model: string;
    repetition_penalty?: number | null;
    temperature?: number | null;
    top_k?: number | null;
    top_p?: number | null;
  };
  llm_type: string;
  model_type: 'evaluate';
  name: string;
  template_option: {
    headers: Array<{ key: string; value: string }>;
    request_body: string;
    response_extract_rule: string;
  };
}) {
  return request.post<API.LLMList>(API_BASE + '/model/create', data
  );
}

/**
 * 删除评估模型
 * 根据模型ID删除指定的评估模型配置
 *
 * @param path - 路径参数
 * @param path.id - 要删除的模型ID
 * @returns Promise<API.LLMList> 返回删除操作结果
 */
export function removeEvalLLM(path: { id: string }) {
  const url = API_BASE + `/model/${path.id}`;
  return request.delete<API.LLMList>(url);
}

/**
 * 获取评估模型详情
 * 根据模型ID获取具体的评估模型配置信息
 *
 * @param path - 路径参数
 * @param path.id - 模型唯一标识符
 * @returns Promise<API.LLMList> 返回模型详细信息
 */
export function getEvalLLM(
  path: { id: string },
) {
  const url = API_BASE + `/model/${path.id}`;
  return request.get<API.LLMList>(url);
}
