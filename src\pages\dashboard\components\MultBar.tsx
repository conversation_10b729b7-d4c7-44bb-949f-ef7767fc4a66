import type { EChartsCoreOption } from "echarts";
import { useEffect } from "react";
import { useEcharts } from "@/hooks/useEcharts";
import { useCommonStore } from "@/hooks/useCommonStore";

let colorList = ["#EC4F4F", "#FA8C16"];

const option: EChartsCoreOption = {
  color: colorList,
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
  },
  legend: {
    top: "5%",
    itemGap: 16,
    itemWidth: 12,
    itemHeight: 4,
    icon: "roundRect",
    itemStyle: {
      borderRadius: 2,
    },
    textStyle: {
      color: "#7E7E7E",
    },
  },
  grid: {
    left: "2%",
    right: "6%",
    bottom: "11%",
    top: "13%",
    containLabel: true,
  },
  xAxis: {
    type: "value",
    boundaryGap: [0, 0.01],
    axisTick: {
      show: false,
    },
    splitLine: {
      show: true,
      lineStyle: {
        type: "dashed",
      },
    },
  },
  yAxis: {
    axisTick: {
      show: false,
    },
    type: "category",
    axisLine: { show: false },
    data: [
      "资产名称aa",
      "资产名称bbb",
      "这里是资产名称c",
      "这里是资产名称dddd",
      "这里是资产名称",
    ],
  },
  series: [
    {
      name: "高危",
      type: "bar",
      barWidth: 8,
      itemStyle: {
        barBorderRadius: [3, 3, 3, 3],
      },
      data: [10, 13, 18, 23, 29],
    },
    {
      name: "中危",
      type: "bar",
      barWidth: 8,
      itemStyle: {
        barBorderRadius: [3, 3, 3, 3],
      },
      data: [12, 13, 19, 23, 28],
    },
  ],
};
function MultBar() {
  const { permissions } = useCommonStore();
  const [echartsRef, init] = useEcharts(option);

  useEffect(() => {
    if (permissions.length) {
      setTimeout(() => {
        init();
      }, 100);
    }
  }, [init, permissions.length]);

  return (
    <div className="h-350px">
      <div ref={echartsRef} className="w-full h-full"></div>
    </div>
  );
}

export default MultBar;
