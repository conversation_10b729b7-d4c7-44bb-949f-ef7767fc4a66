
/**
 * 大模型测评服务 - 报告管理API接口
 *
 * 本文件包含了测评报告和任务管理相关的API接口定义，主要包括：
 * - 评估报告详情查询接口
 * - 报告总览数据接口
 * - 任务列表管理接口
 *
 * <AUTHOR>
 * @version 5.0
 * @since 2024
 */

import { request } from '@/servers/request';
/** API基础路径 */
const API_BASE = "/api";



//获取评估报告
export async function getTaskReportSummaryDetail(data: any) {
    try {
      const res = await request('/llmapi/report/detail', {
        method: 'POST',
        data: data,
      })
      console.log("resp data:",res,res.data);
      return {
        data: res.data,
        success: true,
      };
    } catch (error) {
      console.error('获取报告总结失败', error);
      return { data: [], total: 0, success: false };
    }
  }

// 获取报告总览

export async function getReportSummary(data: any ) {
    try {
      const res = await request('/llmapi/report/summary', {
        method: 'POST',
        data: data,
      })
      console.log("resp data:",res,res.data);
      return {
        data: res.data,
        success: true,
      };
    } catch (error) {
      console.error('获取报告总结失败', error);
      return { data: [], total: 0, success: false };
    }
  }





//获取评估报告
export async function getMissionList(data: any  ) {
    try {
      const res = await request<API.MissionItem>('/llmapi/mission/list', {
        method: 'POST',
        data: data,
      })
      console.log("resp data:",res,res.data);
      return {
        data: res.data,
        total: res.total,
        success: true,
      };
    } catch (error) {
      console.error('获取任务列表失败', error);
      return { data: [], total: 0, success: false };
    }
  }


  