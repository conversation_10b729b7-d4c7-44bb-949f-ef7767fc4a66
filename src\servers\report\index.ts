
/**
 * 大模型测评服务 - 报告管理API接口
 *
 * 本文件包含了测评报告和任务管理相关的API接口定义，主要包括：
 * - 评估报告详情查询接口
 * - 报告总览数据接口
 * - 任务列表管理接口
 *
 * <AUTHOR>
 * @version 5.0
 * @since 2024
 */

import { request } from '@/servers/request';
/** API基础路径 */
const API_BASE = "/api";
/**
 * 报告查询参数接口
 */
interface ReportQueryParams {
  /** 报告ID或任务ID */
  id?: string;
  /** 查询类型 */
  type?: string;
  /** 其他查询参数 */
  [key: string]: string | number | boolean | undefined;
}

/**
 * 任务查询参数接口
 */
interface MissionQueryParams {
  /** 每页显示数量 */
  limit?: number;
  /** 偏移量，用于分页 */
  offset?: number;
  /** 搜索关键词 */
  keyword?: string;
  /** 任务状态筛选 */
  status?: string;
  /** 其他查询参数 */
  [key: string]: string | number | boolean | undefined;
}

/**
 * 统一的API响应接口
 */
interface ApiResponse<T = unknown> {
  /** 响应数据 */
  data: T;
  /** 数据总数（用于分页） */
  total?: number;
  /** 请求是否成功 */
  success: boolean;
  /** 错误信息（失败时） */
  message?: string;
}

/* ==================== 评估报告相关接口 ==================== */

/**
 * 获取任务报告详情
 * 根据任务ID获取详细的评估报告数据
 *
 * @param data - 查询参数
 * @param data.id - 任务ID
 * @param data.type - 可选，报告类型
 * @returns Promise<ApiResponse> 返回报告详情数据
 */
export function getTaskReportSummaryDetail(data: ReportQueryParams): Promise<ApiResponse> {
  return request.post(API_BASE'/llmapi/report/detail', data);
}

/**
 * 获取报告总览
 * 获取报告的汇总统计信息和概览数据
 *
 * @param data - 查询参数
 * @param data.id - 可选，特定报告ID
 * @param data.type - 可选，报告类型
 * @returns Promise<ApiResponse> 返回报告总览数据
 */
export function getReportSummary(data: ReportQueryParams): Promise<ApiResponse> {
  return request.post('/llmapi/report/summary', data);
}

/* ==================== 任务管理相关接口 ==================== */

/**
 * 获取任务列表
 * 获取测评任务列表，支持分页查询和条件筛选
 *
 * @param data - 查询参数
 * @param data.limit - 可选，每页显示数量
 * @param data.offset - 可选，偏移量，用于分页
 * @param data.keyword - 可选，搜索关键词
 * @param data.status - 可选，任务状态筛选
 * @returns Promise<ApiResponse<API.MissionItem[]>> 返回任务列表数据
 */
export function getMissionList(data: MissionQueryParams): Promise<ApiResponse<API.MissionItem[]>> {
  try {
    const res = await request.post('/llmapi/report/summary', data);
    console.log("报告总览响应数据:", res, res.data);
    return {
      data: res.data,
      success: true,
    };
  } catch (error) {
    console.error('获取报告总览失败', error);
    return {
      data: null,
      success: false,
      message: '获取报告总览失败'
    };
  }
}

/* ==================== 任务管理相关接口 ==================== */

/**
 * 获取任务列表
 * 获取测评任务列表，支持分页查询和条件筛选
 *
 * @param data - 查询参数
 * @param data.limit - 可选，每页显示数量
 * @param data.offset - 可选，偏移量，用于分页
 * @param data.keyword - 可选，搜索关键词
 * @param data.status - 可选，任务状态筛选
 * @returns Promise<ApiResponse<API.MissionItem[]>> 返回任务列表数据
 */
export function getMissionList(data: MissionQueryParams): Promise<ApiResponse<API.MissionItem[]>> {
  try {
    const res = await request.post<{
      data: API.MissionItem[];
      total: number;
    }>('/llmapi/mission/list', data);
    console.log("任务列表响应数据:", res, res.data);

    // 从响应中提取数据和总数
    const responseData = res.data || res;
    const missionData = responseData.data || [];
    const totalCount = responseData.total || 0;

    return {
      data: missionData,
      total: totalCount,
      success: true,
    };
  } catch (error) {
    console.error('获取任务列表失败', error);
    return {
      data: [],
      total: 0,
      success: false,
      message: '获取任务列表失败'
    };
  }
}