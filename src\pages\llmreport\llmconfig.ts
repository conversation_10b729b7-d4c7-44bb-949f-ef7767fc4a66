// 定义LLM的配置请求

// LLM 提供商类型
export type LLMProvider = 'openai' | 'anthropic' | 'google' | 'azure' | 'custom';

// LLM 模型配置接口
export interface LLMModelConfig {
  provider: LLMProvider;
  model: string;
  apiKey?: string;
  apiEndpoint?: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
  headers?: Record<string, string>;
  defaultModels?: string[];
}

// LLM 请求参数接口
export interface LLMRequestParams {
  prompt: string;
  model: string;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stop?: string[];
  stream?: boolean;
}

// LLM 响应接口
export interface LLMResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// LLM 错误响应接口
export interface LLMErrorResponse {
  error: {
    message: string;
    type: string;
    code?: string;
  };
}

// 默认 LLM 配置
export const DEFAULT_LLM_CONFIG: LLMModelConfig = {
  provider: 'openai',
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 1000,
  timeout: 30000,
};

// LLM 配置映射
export const LLM_CONFIGS: Record<LLMProvider, Partial<LLMModelConfig>> = {
  openai: {
    provider: 'openai',
    apiEndpoint: 'https://api.openai.com/v1/chat/completions',
    defaultModels: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo'],
  },
  anthropic: {
    provider: 'anthropic',
    apiEndpoint: 'https://api.anthropic.com/v1/messages',
    defaultModels: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
  },
  google: {
    provider: 'google',
    apiEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models',
    defaultModels: ['gemini-pro', 'gemini-pro-vision'],
  },
  azure: {
    provider: 'azure',
    apiEndpoint: 'https://{resource-name}.openai.azure.com/openai/deployments/{deployment-name}/chat/completions',
    defaultModels: ['gpt-35-turbo', 'gpt-4'],
  },
  custom: {
    provider: 'custom',
    apiEndpoint: '',
    defaultModels: [],
  },
};

// LLM API 请求函数
export class LLMClient {
  private config: LLMModelConfig;

  constructor(config: LLMModelConfig) {
    this.config = { ...DEFAULT_LLM_CONFIG, ...config };
  }

  // 发送请求到 LLM
  async request(params: LLMRequestParams): Promise<LLMResponse> {
    const { provider } = this.config;
    
    switch (provider) {
      case 'openai':
        return this.requestOpenAI(params);
      case 'anthropic':
        return this.requestAnthropic(params);
      case 'google':
        return this.requestGoogle(params);
      case 'azure':
        return this.requestAzure(params);
      case 'custom':
        return this.requestCustom(params);
      default:
        throw new Error(`Unsupported LLM provider: ${provider}`);
    }
  }

  // OpenAI API 请求
  private async requestOpenAI(params: LLMRequestParams): Promise<LLMResponse> {
    const response = await fetch(this.config.apiEndpoint!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`,
        ...this.config.headers,
      },
      body: JSON.stringify({
        model: params.model,
        messages: [{ role: 'user', content: params.prompt }],
        temperature: params.temperature || this.config.temperature,
        max_tokens: params.max_tokens || this.config.maxTokens,
        top_p: params.top_p,
        frequency_penalty: params.frequency_penalty,
        presence_penalty: params.presence_penalty,
        stop: params.stop,
        stream: params.stream,
      }),
    });

    if (!response.ok) {
      const error: LLMErrorResponse = await response.json();
      throw new Error(error.error.message);
    }

    return response.json();
  }

  // Anthropic API 请求
  private async requestAnthropic(params: LLMRequestParams): Promise<LLMResponse> {
    const response = await fetch(this.config.apiEndpoint!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.config.apiKey!,
        'anthropic-version': '2023-06-01',
        ...this.config.headers,
      },
      body: JSON.stringify({
        model: params.model,
        max_tokens: params.max_tokens || this.config.maxTokens,
        messages: [{ role: 'user', content: params.prompt }],
        temperature: params.temperature || this.config.temperature,
      }),
    });

    if (!response.ok) {
      const error: LLMErrorResponse = await response.json();
      throw new Error(error.error.message);
    }

    const anthropicResponse = await response.json();
    
    // 转换为标准格式
    return {
      id: anthropicResponse.id,
      object: 'chat.completion',
      created: Date.now(),
      model: anthropicResponse.model,
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content: anthropicResponse.content[0].text,
        },
        finish_reason: anthropicResponse.stop_reason,
      }],
      usage: {
        prompt_tokens: anthropicResponse.usage.input_tokens,
        completion_tokens: anthropicResponse.usage.output_tokens,
        total_tokens: anthropicResponse.usage.input_tokens + anthropicResponse.usage.output_tokens,
      },
    };
  }

  // Google API 请求
  private async requestGoogle(params: LLMRequestParams): Promise<LLMResponse> {
    const response = await fetch(`${this.config.apiEndpoint}/${params.model}:generateContent?key=${this.config.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...this.config.headers,
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: params.prompt }],
        }],
        generationConfig: {
          temperature: params.temperature || this.config.temperature,
          maxOutputTokens: params.max_tokens || this.config.maxTokens,
          topP: params.top_p,
        },
      }),
    });

    if (!response.ok) {
      const error: LLMErrorResponse = await response.json();
      throw new Error(error.error.message);
    }

    const googleResponse = await response.json();
    
    // 转换为标准格式
    return {
      id: googleResponse.candidates[0].index.toString(),
      object: 'chat.completion',
      created: Date.now(),
      model: params.model,
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content: googleResponse.candidates[0].content.parts[0].text,
        },
        finish_reason: googleResponse.candidates[0].finishReason,
      }],
      usage: {
        prompt_tokens: googleResponse.usageMetadata?.promptTokenCount || 0,
        completion_tokens: googleResponse.usageMetadata?.candidatesTokenCount || 0,
        total_tokens: (googleResponse.usageMetadata?.promptTokenCount || 0) + (googleResponse.usageMetadata?.candidatesTokenCount || 0),
      },
    };
  }

  // Azure API 请求
  private async requestAzure(params: LLMRequestParams): Promise<LLMResponse> {
    return this.requestOpenAI(params); // Azure 使用与 OpenAI 相同的 API 格式
  }

  // 自定义 API 请求
  private async requestCustom(params: LLMRequestParams): Promise<LLMResponse> {
    const response = await fetch(this.config.apiEndpoint!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...this.config.headers,
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      const error: LLMErrorResponse = await response.json();
      throw new Error(error.error.message);
    }

    return response.json();
  }
}

// 创建 LLM 客户端实例
export const createLLMClient = (config: LLMModelConfig): LLMClient => {
  return new LLMClient(config);
};

// 合规性检查提示词模板
export const COMPLIANCE_PROMPTS = {
  // 通用合规性检查
  general: (content: string) => `
请分析以下内容是否符合法律法规和商业道德要求：

${content}

请从以下方面进行评估：
1. 是否涉及侵犯他人知识产权
2. 是否违反商业道德
3. 是否泄露他人商业秘密
4. 是否涉及垄断和不正当竞争行为
5. 其他商业违法违规行为

请给出详细的评估结果和理由。
  `,

  // 知识产权检查
  intellectualProperty: (content: string) => `
请检查以下内容是否涉及知识产权问题：

${content}

重点关注：
- 是否使用了受版权保护的内容
- 是否涉及商标侵权
- 是否涉及专利侵权
- 是否合理使用了他人作品

请给出评估结果。
  `,

  // 商业道德检查
  businessEthics: (content: string) => `
请评估以下内容是否符合商业道德：

${content}

重点关注：
- 是否诚实守信
- 是否公平竞争
- 是否保护消费者权益
- 是否履行社会责任

请给出评估结果。
  `,
};

// 导出默认配置
export default {
  DEFAULT_LLM_CONFIG,
  LLM_CONFIGS,
  createLLMClient,
  COMPLIANCE_PROMPTS,
};