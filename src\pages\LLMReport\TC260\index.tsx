import React, { useState, useEffect, useRef } from 'react';
import { Spin, Typography, Space, message, Layout, Anchor, Button, Card, Menu, Divider } from 'antd';
import { MenuOutlined, CloseOutlined, SafetyCertificateOutlined, SettingOutlined, UserOutlined, LeftOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';

// 引入自定义组件
import ComplianceMethod from '../ComplianceMethod';
import Content2 from './Content2';

// 引入后端API
import { getTestllms } from "@/servers/llms";
import { getReportSummary } from '@/servers/report';
import { getTask } from '@/servers/evaluation';
import './index.less';
import Content1 from './Content1';
import { getPolicy } from "@/servers/configuration";
import { Header } from 'antd/es/layout/layout';
// 引入图片
import logo from '@/static/assets/logo.png';
import bg from '@/static/assets/bg.png';


const { Sider, Content } = Layout;
const { Title, Paragraph, Text } = Typography;
const { Link } = Anchor;


const TC260Report: React.FC = (props: any) => {
    const { taskId } = props;
    const contentRef = useRef(null);
    const [loading, setLoading] = useState(true);
    const [template, setTemplate] = useState([])
    const [testModel, setTestModel] = useState({})
    const [scanPolicy, setScanPolicy] = useState({})
    const [task, setTask] = useState({})
    const [siderVisible, setSiderVisible] = useState(true); // 新增状态控制侧边栏显示

    // 初始化五个 Card 的展开状态
    const [cardExpandedStates, setCardExpandedStates] = useState<Record<number, boolean>>({
        1: true,
        2: true,
        3: true,
        4: true,
        5: true
    });

    // 切换 Card 展开状态的函数
    const toggleCardExpanded = (cardIndex: number) => {
        setCardExpandedStates(prevStates => ({
            ...prevStates,
            [cardIndex]: !prevStates[cardIndex]
        }));
    };

    // 定义 初始化报告数据

    const initReportData = async () => {
        // 获取任务明细
        const resp_task = await getTask(taskId)
        if (resp_task.status === 200) {
            const searchTask = resp_task.data
            console.log("查询task:", searchTask);
            setTask(resp_task.data || {})
            // 获取报告初始化模板-分类树
            const resp_summary = await getReportSummary({ "task_id": taskId })
            console.log("获取的模板：", resp_summary.data);
            setTemplate(resp_summary?.data || [])
            // 获取模型信息
            const resp_llm = await getTestllms({ id: searchTask?.model_id })
            console.log("获取的模型信息：", resp_llm.data);

            setTestModel(resp_llm?.data || {})
            const resp_strategy = await getPolicy(resp_task?.data?.strategy_id)
            setScanPolicy(resp_strategy?.data || {})

        }
        else {
            message.error("未找到对应扫描任务，无法显示报告")
        }

    };
    useEffect(() => {
        setLoading(true)
        // 获取报告模板预览
        initReportData()
        // 获取策略信息
        setLoading(false)

    }, [taskId]);
    // 时间转换函数
    const formatDate = (isoDate: string | undefined): string => {
        if (!isoDate) {
            return '';
        }
        const date = new Date(isoDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };
    // 导出 HTML 报告
    const exportHTML = async () => {
        const contentElement = document.querySelector('.content-overlay');
        if (contentElement) {
            // 深克隆内容元素，避免修改原始 DOM
            let clonedElement = contentElement.cloneNode(true) as HTMLElement;

            // 处理图片资源
            const images = clonedElement.querySelectorAll('img');
            for (const img of Array.from(images)) {
                const src = img.src;
                if (src) {
                    try {
                        const response = await fetch(src);
                        const blob = await response.blob();
                        const dataUrl = URL.createObjectURL(blob);
                        img.src = dataUrl;
                    } catch (error) {
                        console.error('Failed to fetch image:', error);
                    }
                }
            }

            // 处理 CSS 资源
            const styleSheets = document.styleSheets;
            let combinedStyles = '';
            for (let i = 0; i < styleSheets.length; i++) {
                const styleSheet = styleSheets[i] as CSSStyleSheet;
                try {
                    const cssRules = styleSheet.cssRules;
                    for (let j = 0; j < cssRules.length; j++) {
                        let cssText = cssRules[j].cssText;
                        // 处理 CSS 中的图片、字体等资源
                        const urlRegex = /url\(['"]?([^'")]+)['"]?\)/g;
                        // 修复 replace 中异步函数的问题
                        const processUrl = (match: string, url: string) => {
                            try {
                                const fullUrl = new URL(url, window.location.href).href;
                                // 这里使用同步方式处理可能会有问题，建议使用 Promise 处理
                                // 但为了简化示例，暂时不做异步处理，实际项目中可优化
                                return match;
                            } catch (error) {
                                console.error('Failed to fetch CSS resource:', error);
                                return match;
                            }
                        };
                        cssText = cssText.replace(urlRegex, processUrl);
                        combinedStyles += cssText;
                    }
                } catch (error) {
                    // 跨域样式表可能会抛出安全错误
                    console.error('Failed to read CSS rules:', error);
                }
            }

            // 处理脚本资源
            const scripts = clonedElement.querySelectorAll('script');
            for (const script of Array.from(scripts)) {
                const src = script.src;
                if (src) {
                    try {
                        const response = await fetch(src);
                        const scriptText = await response.text();
                        const newScript = document.createElement('script');
                        newScript.textContent = scriptText;
                        script.parentNode?.replaceChild(newScript, script);
                    } catch (error) {
                        console.error('Failed to fetch script:', error);
                    }
                }
            }

            // 创建 style 元素并插入到克隆元素的头部
            const styleElement = document.createElement('style');
            styleElement.textContent = combinedStyles;
            const head = document.createElement('head');
            // 添加字符编码声明
            const metaCharset = document.createElement('meta');
            metaCharset.setAttribute('charset', 'UTF-8');
            head.appendChild(metaCharset);
            head.appendChild(styleElement);
            const html = document.createElement('html');
            html.appendChild(head);
            html.appendChild(clonedElement);

            const htmlContent = html.outerHTML;
            const blob = new Blob([htmlContent], { type: 'text/html;charset=UTF-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            // 获取当前报告名称，若名称不存在则使用默认文件名
            const reportName = (testModel as { name?: string }).name || '绿盟大模型安全测评报告';
            // 替换文件名中的非法字符
            const safeReportName = reportName.replace(/[\\/*?:"<>|]/g, '_');
            a.download = `${safeReportName}.html`;
            a.click();
            URL.revokeObjectURL(url);
        }
    };
    return (
        <div
        // style={{ height: 'calc(100vh - 64px)' }}
        >
            <Spin spinning={loading}>
                {/* 全局头部组件 */}
                <Layout>
                    <Header
                        className="global-header"
                    >
                        <div className="header-content header-flex" style={{ alignItems: 'center' }}>
                            {/* 左侧标题 */}
                            <div className="header-left">
                                <h1 style={{ margin: 0, fontSize: '24px' }}>绿盟大模型安全测评报告</h1>
                            </div>
                            {/* 右侧图标 */}
                            <div className="header-right">
                                <Space size="large">
                                    <img
                                        src={logo}
                                        alt="logo"
                                        style={{
                                            width: '100%',
                                            height: '150%',
                                        }}
                                    />
                                </Space>
                            </div>
                        </div>
                    </Header>
                    <Layout className="llm-report-content" style={{ paddingTop: '0px', height: 'calc(100vh - 64px)' }}>


                        {/* 切换按钮 */}
                        <div className={siderVisible ? 'sider-visible' : 'sider-hidden'}>
                            <Button
                                type="text"
                                icon={siderVisible ? <LeftOutlined /> : <MenuOutlined rotate={90} />}
                                onClick={() => setSiderVisible(!siderVisible)}
                                className="toggle-sider-btn"
                                style={{ padding: 0 }}
                            />
                        </div>
                        {/* 左侧菜单栏 */}
                        {siderVisible && (
                            <Sider style={{ background: '#fff', position: 'sticky', top: 0 }} width={230}>
                                <Menu
                                    mode="inline"
                                    // 设置默认展开的菜单项 key
                                    defaultOpenKeys={['section1', 'section2', 'section3', 'section4', 'section5']}
                                    items={[
                                        {
                                            key: 'menu-title',
                                            label: '目录',
                                            disabled: true,
                                            className: 'menu-title'
                                        },
                                        {
                                            key: 'section1',
                                            label: '01 基本信息',
                                            children: [
                                                { key: 'section1.1', label: '1.1 评估工具介绍' },
                                                { key: 'section1.2', label: '1.2 测试模型基本信息' },
                                                { key: 'section1.3', label: '1.3 评估委托方信息' },
                                            ],
                                        },
                                        { key: 'section2', label: '02 评估依据与范围' },
                                        {
                                            key: 'section3',
                                            label: '03 详细评估内容',
                                            children: [
                                                { key: 'section3.1', label: '3.1 评估指标' },
                                                { key: 'section3.2', label: '3.2 评估细节' },
                                            ],
                                        },
                                        { key: 'section4', label: '04 缓解方案' },
                                        { key: 'section5', label: '05 结论与建议' },
                                    ]}
                                    onClick={({ key }) => {
                                        if (key === 'menu-title') return;
                                        const element = document.getElementById(key);
                                        const header = document.querySelector('.global-header') as HTMLElement;
                                        const headerHeight = header ? header.offsetHeight : 0;

                                        if (element) {
                                            element.scrollIntoView({ behavior: 'smooth' });
                                          
                                            window.scrollBy(0, -headerHeight);
                                        }
                                    }}
                                />
                            </Sider>
                        )}

                        {/* 右侧报告内容 */}
                        <Content
                            style={{
                                background: '#f7f8fa',
                                padding: '20px',

                                overflowY: 'scroll',
                                position: 'sticky' // 添加相对定位，以便子元素绝对定位
                            }}
                            ref={contentRef}
                        >
                            <div className="background-image-container">
                                <img
                                    src={bg}
                                    alt="Background"
                                    style={{
                                        width: '100%',
                                        height: '100%',
                                        objectFit: 'cover',
                                        objectPosition: 'center'
                                    }}
                                />
                            </div>
                            <div className="content-overlay">
                                <Title level={1} style={{ marginBottom: 20 }}>
                                    {testModel.name}
                                </Title>
                                <div style={{ display: 'flex', marginBottom: 20, width: '100%', justifyContent: 'space-between' }} >
                                    <Space direction="horizontal" style={{ justifyContent: 'flex-start' }}>
                                        <Space direction="horizontal" align="center">
                                            <Text style={{ color: 'gray' }}>创建时间：</Text>

                                            <Text strong style={{ fontWeight: 'bold' }}>{formatDate((testModel as { create_time?: string }).create_time)}</Text>
                                        </Space>
                                        <Divider type="vertical" className="thick-divider" />
                                        <Space direction="horizontal" align="center">
                                            <Text style={{ color: 'gray' }}>委托单位：</Text>
                                            <Text strong style={{ fontWeight: 'bold' }}>无</Text>
                                        </Space>
                                        <Divider type="vertical" className="thick-divider" />
                                        <Space direction="horizontal" align="center">
                                            <Text style={{ color: 'gray' }}>评估单位：</Text>
                                            <Text strong style={{ fontWeight: 'bold' }}>绿盟科技天枢实验室</Text>
                                        </Space>
                                    </Space>
                                    <Button type="primary" className="download-report-btn" onClick={exportHTML}>下载离线报表</Button>
                                </div>

                                <Card id="section1" style={{ marginBottom: 20 }}>
                                    <Title level={2}>
                                        <span className="title-large-number">01</span>
                                        <span className="title-subtext">基本信息</span>
                                        {cardExpandedStates[1] && (
                                            <>
                                                <Content1 task={task} testModel={testModel} scanPolicy={scanPolicy} />
                                                <div style={{ textAlign: 'right' }}>
                                                    {/* 添加统一类名 */}
                                                    <Button type="link" className="toggle-card-btn" onClick={() => toggleCardExpanded(1)}>
                                                        收起<UpOutlined />
                                                    </Button>
                                                </div>
                                            </>
                                        )}
                                        {!cardExpandedStates[1] && (
                                            <div style={{ textAlign: 'right' }}>
                                                {/* 添加统一类名 */}
                                                <Button type="link" className="toggle-card-btn" onClick={() => toggleCardExpanded(1)}>
                                                    展开<DownOutlined />
                                                </Button>
                                            </div>
                                        )}
                                    </Title>

                                </Card>
                                <Card id="section2" style={{ marginBottom: 20 }}>
                                    <Title level={2}>
                                        <span className="title-large-number">02</span>
                                        <span className="title-subtext">评估依据与范围</span>
                                    </Title>
                                    {cardExpandedStates[2] && (
                                        <>
                                            <Content2 />
                                            <div style={{ textAlign: 'right' }}>
                                                {/* 添加统一类名 */}
                                                <Button type="link" className="toggle-card-btn" onClick={() => toggleCardExpanded(2)}>
                                                    收起<UpOutlined />
                                                </Button>
                                            </div>
                                        </>
                                    )}
                                    {!cardExpandedStates[2] && (
                                        <div style={{ textAlign: 'right' }}>
                                            {/* 添加统一类名 */}
                                            <Button type="link" className="toggle-card-btn" onClick={() => toggleCardExpanded(2)}>
                                                展开<DownOutlined />
                                            </Button>
                                        </div>
                                    )}
                                </Card>
                                <Card id="section3" style={{ marginBottom: 20 }}>
                                    <Title level={2}>
                                        <span className="title-large-number">03</span>
                                        <span className="title-subtext">详细评估内容</span>
                                    </Title>
                                    {cardExpandedStates[3] && (
                                        <>
                                            <ComplianceMethod template={template || []} task={task} />
                                            <div style={{ textAlign: 'right' }}>
                                                {/* 添加统一类名 */}
                                                <Button type="link" className="toggle-card-btn" onClick={() => toggleCardExpanded(3)}>
                                                    收起<UpOutlined />
                                                </Button>
                                            </div>
                                        </>
                                    )}
                                    {!cardExpandedStates[3] && (
                                        <div style={{ textAlign: 'right' }}>
                                            {/* 添加统一类名 */}
                                            <Button type="link" className="toggle-card-btn" onClick={() => toggleCardExpanded(3)}>
                                                展开<DownOutlined />
                                            </Button>
                                        </div>
                                    )}
                                </Card>
                                <Card id="section4" style={{ marginBottom: 20 }}>
                                    <Title level={2}>
                                        <span className="title-large-number">04</span>
                                        <span className="title-subtext">缓解方案</span>
                                    </Title>
                                    {cardExpandedStates[4] && (
                                        <>
                                            <div style={{ textAlign: 'right' }}>
                                                {/* 添加统一类名 */}
                                                <Button type="link" className="toggle-card-btn" onClick={() => toggleCardExpanded(4)}>
                                                    收起<UpOutlined />
                                                </Button>
                                            </div>
                                        </>
                                    )}
                                    {!cardExpandedStates[4] && (
                                        <div style={{ textAlign: 'right' }}>
                                            {/* 添加统一类名 */}
                                            <Button type="link" className="toggle-card-btn" onClick={() => toggleCardExpanded(4)}>
                                                展开<DownOutlined />
                                            </Button>
                                        </div>
                                    )}
                                </Card>
                                <Card id="section5" style={{ marginBottom: 20 }}>
                                    <Title level={2}>
                                        <span className="title-large-number">05</span>
                                        <span className="title-subtext">结论与建议</span>
                                    </Title>
                                    {cardExpandedStates[5] && (
                                        <>
                                            <Card className="recommendation-card">
                                                <ul className="recommendation-list">
                                                    <li className="recommendation-intro">为了有效缓解大模型风险，绿盟科技建议以下缓解措施：</li>
                                                    <li className="recommendation-item">
                                                        在训练阶段应加强对训练数据的保护，包括对敏感信息的处理。可以通过数据脱敏、匿名化或加密技术来实现，以确保训练数据的安全性和隐私性。
                                                    </li>
                                                    <li className="recommendation-item">
                                                        在部署阶段，应加强对于输入输出的过滤，以防止恶意输入导致模型行为异常或输出不当内容。大模型安全技术可以有效地监控和管理模型的输入输出，确保其符合预期的安全标准。
                                                    </li>
                                                    <li className="recommendation-item">
                                                        此外应建立一套机制，及时基于新法规对模型进行合规性评估。这包括定期更新合规性检查列表，以确保模型符合最新的法律法规要求，并及时调整模型以满足新的合规标准。
                                                    </li>
                                                </ul>
                                            </Card>
                                            <div style={{ textAlign: 'right' }}>
                                                {/* 添加统一类名 */}
                                                <Button type="link" className="toggle-card-btn" onClick={() => toggleCardExpanded(5)}>
                                                    收起<UpOutlined />
                                                </Button>
                                            </div>
                                        </>
                                    )}
                                    {!cardExpandedStates[5] && (
                                        <div style={{ textAlign: 'right' }}>
                                            {/* 添加统一类名 */}
                                            <Button type="link" className="toggle-card-btn" onClick={() => toggleCardExpanded(5)}>
                                                展开<DownOutlined />
                                            </Button>
                                        </div>
                                    )}
                                </Card>
                            </div>
                        </Content>

                    </Layout>
                </Layout>
            </Spin>
        </div>
    )
}

export default TC260Report;


