import { useState, useRef, useEffect } from 'react';
import { message, Tree, Tabs, Form, Layout, Card, ColorPicker, Space } from 'antd';
import { ProjectOutlined } from '@ant-design/icons';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import { PageContainer } from '@ant-design/pro-layout';
import { getDictList, getDictTree, getDictDetail, updateDictDetail } from '@/servers/configuration';
import { ModalForm, ProCard, ProDescriptions, ProDescriptionsItemProps, ProForm, ProFormDigit, ProFormGroup, ProFormSelect, ProFormText, ProFormTextArea, StatisticCard } from '@ant-design/pro-components';

const { Header, Content, Footer, Sider } = Layout;

import { Divider, Typography } from 'antd';

const { Title, Paragraph, Text, Link } = Typography;
interface TreeNode {
  name: string;
  subclass?: Record<string, any>;  // 修改为字典类型
}

interface TreeDataNode {
  title: string;
  key: string;
  children?: TreeDataNode[];
}

const processNode = (parentKey: string, node: TreeNode): TreeDataNode => {
  if (!node.subclass || Object.keys(node.subclass).length === 0) {
    return {
      title: node.name,
      key: parentKey + '.' + node.name,
    };
  }

  return {
    title: node.name,
    key: node.name,
    children: Object.keys(node.subclass).map(subKey => {
      const childNode = processNode(parentKey + '.' + subKey, node.subclass![subKey]);
      return {
        title: subKey,
        key: parentKey + '.' + subKey,
        children: childNode.children
      };
    }),
  };
};

export default function Dictionary() {
  const [currentRow, setCurrentRow] = useState<API.LLMItem>();
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const actionRef = useRef<ActionType>();
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [editingUseCase, setEditingUseCase] = useState<API.UsecaseItem | null>(null);
  const [editForm] = Form.useForm();
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showData, setShowData] = useState<any[]>([]);
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]); // 用于存储树形数据
  const [loading, setLoading] = useState<boolean>(false);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  // 获取表单实例
  const [form] = Form.useForm();
  // 修改 onEdit 函数
  const onEdit = (record: API.UsecaseItem) => {
    setCurrentRow(record);
    setModalOpen(true);
  };

  // 修改 handleUpdateUseCase 函数
  const handleUpdateUseCase = async (values: any) => {
    try {
      if (!currentRow) return false;

      const updateData = {
        id: currentRow.id,
        payload: Array.isArray(values.payload) ? values.payload : [values.payload],
      };

      const response = await updateUseCase(updateData);
      if (response.status === 200) {
        message.success('用例更新成功');
        setModalOpen(false);
        setCurrentRow(undefined);


        // 刷新表格数据

        setPrompts(prev => prev.map(item => item.id === currentRow.id ? { ...item, ...updateData } : item));
        actionRef.current?.reload();

        // 刷新当前选中节点的统计数据
        if (selectedKeys.length > 0) {
          const selectedId = selectedKeys[0] as string;
          fetchStatistics(selectedId);

        }


        return true;
      } else {
        message.error('用例更新失败');
        return false;
      }
    } catch (error) {
      console.error('更新用例失败:', error);
      message.error('更新用例失败');
      return false;
    }
  };
  // 添加 handleView 函数
  const handleView = (record: API.UsecaseItem) => {
    setCurrentRow(record);
    setShowDetail(true);
  };

  //删除用例
  const onDelete = async (record: API.UsecaseItem) => {
    try {
      const res = await removeUseCase({ id: record.id });
      if (res) {
        message.success('删除成功');
        // 刷新表格
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };
  // 获取树形数据的函数
  const fetchTreeData = async (parent_id?: string) => {
    try {
      parent_id = parent_id || '';
      setLoading(true);

      // 初始化时调用 getDictList 获取顶层节点
      const res = await getDictList({
        limit: 1000,
        offset: 1,
        keyword: '',
        parent_id: '',
      });
      // console.log('获取字典列表:', res);

      if (res.status === 200 && res.data) {
        // 将 getDictList 的数据转换为树形结构
        const transformedData = res.data.map(item => ({
          title: item.name || item.id, // 使用 name 作为 title，如果没有则使用 id
          key: item.id,
          children: [], // 初始化时 children 为空数组
          isLeaf: false, // 标记为非叶子节点，表示可以展开
          raw: item
        }));

        setTreeData(transformedData);
      }

    } catch (error) {
      console.error('获取树形数据失败:', error);
      message.error('获取树形数据失败');
    } finally {
      setLoading(false);
    }
  };
  // 处理节点展开事件
  const onExpand = async (expandedKeys: React.Key[], { expanded, node }: any) => {
    setExpandedKeys(expandedKeys);

    // 如果是展开操作且该节点的 children 为空，则加载子节点
    if (expanded && node.children.length === 0) {
      await loadChildNodes(node.key);
    }
  };
  // 将子节点转换为树形结构
  const transformChildData = (nodes: API.DictList): any[] => {
    return nodes.map(node => ({
      title: node.name,
      key: node.id,
      children: node.children && node.children.length > 0
        ? transformChildData(node.children)
        : [],
      isLeaf: !node.children || node.children.length === 0,
      raw: node
    }));
  };
  // 加载子节点的函数
  const loadChildNodes = async (nodeId: string) => {


    try {
      setLoading(true);

      const res = await getDictTree(nodeId);

      if (res && res.status === 200 && res.data) {
        const childNodes = res.data;
        const transformedChildData = transformChildData(childNodes);

        // setTreeData(transformedChildData);
        setTreeData(prevTreeData => {
          const updateNodeChildren = (nodes: TreeDataNode[]): TreeDataNode[] => {
            return nodes.map(node => {
              if (node.key === nodeId) {
                return {
                  ...node,
                  children: transformedChildData,
                  isLeaf: transformedChildData.length === 0
                };
              }
              if (node.children) {
                return {
                  ...node,
                  children: updateNodeChildren(node.children)
                };
              }
              return node;
            });
          };
          return updateNodeChildren(prevTreeData);
        });

      }
    } catch (error) {
      console.error('加载子节点失败:', error);
      message.error('加载子节点失败');
    } finally {
      setLoading(false);
    }
  };
  // 递归查找匹配节点
  const findMatchNodes = (data: any[], keyword: string): string[] => {
    const result: string[] = [];
    data.forEach(item => {
      if (item.title.includes(keyword)) {
        result.push(item.key);
      }
      if (item.children) {
        result.push(...findMatchNodes(item.children, keyword));
      }
    });
    return result;
  };

  // Tree 节点选择时更新表单
  const onSelect = async (keys: string[]) => {
    setSelectedKeys(keys);

    if (keys.length > 0) {
      const selectedId = keys[0] as string;
      console.log('选中的节点ID:', selectedId);

      try {
        // 并行获取字典详情
        const [dictDetailResponse] = await Promise.all([
          getDictDetail(selectedId),
        ]);

        // 更新字典详情数据并联动表单
        if (dictDetailResponse?.status === 200 && dictDetailResponse.data) {
          const dictData = dictDetailResponse.data;
          setShowData(dictData);

          // 更新表单显示最新数据 - 包含所有字段
          form.setFieldsValue({
            name: dictData.name || '',
            // detection_principle: dictData.detection_principle || '',
            color: dictData.color || '',
            order: Number(dictData.order) || 0,
            description: dictData.description || '',
            qrs_operator: dictData.qrs?.operator || '',
            qrs_value: dictData.qrs?.value || 0,
          });

          console.log('字典详情:', dictData);
        }


      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取数据失败');
      }
    } else {

      // 清空表单
      form.resetFields();
    }
  };
// 处理表单提交
const handleFormSubmit = async (values: any) => {
  if (selectedKeys.length === 0) {
    message.warning('请先选择要编辑的字典节点');
    return false;
  }

  try {
    const selectedId = selectedKeys[0] as string;

    // 构建提交数据，包含完整的数据结构
    const submitData = {
      id: selectedId,
      name: values.name,
      // detection_principle: values.detection_principle,
      color: values.color,
      order: values.order,
      description: values.description,
      value: values.value,
      module: values.module,
      qrs: {
        operator: values.qrs_operator,
        value: values.qrs_value,
      }
    };

    const response = await updateDictDetail(selectedId, submitData);

    if (response.status === 200) {
      message.success('字典更新成功');

      // 重新获取数据以刷新显示
      const dictDetailResponse = await getDictDetail(selectedId);
      if (dictDetailResponse?.status === 200 && dictDetailResponse.data) {
        const dictData = dictDetailResponse.data;
        setShowData(dictData);

        // 更新表单显示最新数据
        form.setFieldsValue({
          name: dictData.name || '',
          // detection_principle: dictData.detection_principle || '',
          color: dictData.color || '',
          order: Number(dictData.order) || 0,
          description: dictData.description || '',
          value: dictData.value || '',
          module: dictData.module || '',
          qrs_operator: dictData.qrs?.operator || '',
          qrs_value: dictData.qrs?.value || 0,
        });
      }

      // 更新树形数据中对应节点的标题
      setTreeData(prevTreeData => {
        const updateNodeTitle = (nodes: TreeDataNode[]): TreeDataNode[] => {
          return nodes.map(node => {
            if (node.key === selectedId) {
              return {
                ...node,
                title: values.name
              };
            }
            if (node.children) {
              return {
                ...node,
                children: updateNodeTitle(node.children)
              };
            }
            return node;
          });
        };
        return updateNodeTitle(prevTreeData);
      });

      return true;
    } else {
      message.error('字典更新失败');
      return false;
    }
  } catch (error) {
    console.error('更新字典失败:', error);
    message.error('更新字典失败');
    return false;
  }
};

  //获取统计数据
  const fetchStatistics = async (id: string) => {
    try {
      const res = await getStatistics(id);
      if (res.status === 200) {
        // console.log('获取统计数据:', res.data);

        setShowStatistics(res.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    }
  }
  // 在组件挂载获取数据
  useEffect(() => {

    fetchTreeData(); // 获取树形数据
    // onSelect([id]); // 默认选中当前字典的 ID
  }, []);
  return (
    <PageContainer title={false}>
      <ProCard title={false}>
      <Layout>
        <Sider style={{ background: 'transparent'}} width={350}>
          <Tabs>
            <Tabs.TabPane
              tab={<span><ProjectOutlined />  字典管理</span>}
              key="dataset"
            />
          </Tabs>
          <Tree
            showLine
            treeData={treeData}
            selectedKeys={selectedKeys}
            expandedKeys={expandedKeys}
            onSelect={onSelect}
            onExpand={onExpand}
            loading={loading}
          />
        </Sider>
        <Content style={{ paddingLeft: '20px' }}>

          <ProForm
            style={{ marginTop: 100 }}
            form={form}
            onFinish={handleFormSubmit}
            submitter={{
              searchConfig: {
                submitText: '保存',
                resetText: '重置',
              },
              render: (_, dom) => (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    marginTop: 16,
                  }}
                >
                  <Space>{dom}</Space>
                </div>
              ),
              resetButtonProps: {
                onClick: () => {
                  // 重置表单为当前选中节点的原始数据
                  if (showData) {
                    form.setFieldsValue({
                      name: showData.name || '',
                      // detection_principle: showData.detection_principle || '',
                      color: showData.color || '',
                      order: Number(showData.order) || 0,
                      description: showData.description || '',
                      qrs_operator: showData.qrs?.operator || '',
                      qrs_value: showData.qrs?.value || 0,
                    });
                  }
                }
              }

            }}
          >
            <ProFormGroup>

              <ProFormText
                width="md"
                name="name"
                label="名称"
              />
              <ProFormDigit
                width="md"
                name="order"
                label="order"
                min={0}
                max={100}
                fieldProps={{
                  precision: 0, // 不允许小数
                  step: 1,
                  placeholder: "请输入排序号"
                }}
              />
              <Form.Item
                name="color"
                label="color"
                style={{ width: 'auto' }}
              >
                <ColorPicker
                  showText
                  format="hex"
                  value={form.getFieldValue('color') || '#FFFFFF'}
                  onChange={(color) => {
                    form.setFieldsValue({ color: color.toHexString() });
                  }}
                />
              </Form.Item>
            </ProFormGroup>
            <ProFormGroup title="及格标准">
              <ProFormSelect
                width="md"
                name="qrs_operator"
                label="操作符"
                options={[
                  { label: '大于等于 (gte)', value: 'gte' },
                  { label: '小于等于 (lte)', value: 'lte' },
                  { label: '等于 (eq)', value: 'eq' },
                  { label: '大于 (gt)', value: 'gt' },
                  { label: '小于 (lt)', value: 'lt' },
                ]}
                placeholder="请选择操作符"
              />
              <ProFormDigit
                width="md"
                name="qrs_value"
                label="数值"
                min={0}
                max={100}
                fieldProps={{
                  precision: 0,
                  step: 1,
                  placeholder: "请输入数值"
                }}
              />
            </ProFormGroup>
            <ProFormTextArea
              label="描述"
              name="description"
              
              fieldProps={{
                rows: 8,
                // style: { margin: '-20px' },
                // style: { width: '100%', height: 'calc(40vh - 200px)' },
              }}

            />
          </ProForm>

        </Content>
      </Layout>
      </ProCard>
    </PageContainer>
  );
}


