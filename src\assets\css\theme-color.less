@import url("./default.less");
@import url("./theme.less");

/**
定义了三个主题样式，分别是默认主题（.theme-primary），暗黑主题（.theme-dark），和蓝色主题（.theme-GA）。
每个主题样式通过调用 changeTheme() 混合器，并传入不同的参数来改变主题的样式。
这些参数可以在混合器内部使用，用于定义不同主题的颜色和样式
**/

// 标品主题样式
.theme-primary {
  .changeTheme(); // 调用 changeTheme() 混合器，使用默认参数
}

// 暗黑主题样式
.theme-dark {
  .changeTheme(
    @darkText85, // 混合器参数：深色文本颜色
    @darkContentBg, // 混合器参数：深色内容背景颜色
    @darkLayoutContentBg, // 混合器参数：深色布局内容背景颜色
    @darkContentBg, // 混合器参数：深色内容背景颜色
    @darkSvgColor, // 混合器参数：深色 SVG 颜色
    @darkContentBg, // 混合器参数：深色内容背景颜色
    @darkLoginBg, // 混合器参数：深色登录背景颜色
    @loginBorder, // 混合器参数：登录边框颜色
    @darkBorderColor // 混合器参数：深色边框颜色
  );
}

// 蓝色主题样式
.theme-ga {
  .changeTheme();
}