@layoutTop: 86px;
@layoutLeft: 208px;
@layoutLeftClose: 64px;


// 默认颜色
@primaryColor: #55a722;
@primaryBg: #fff;
@contentBgColor: #fff;

@svgColor: #00000073;
@svgHoverColor: #404040;
@shadowColor: rgba(0, 0, 0, 0.08);
@colorText: #535353;
@memuBg: #26303a;
@loginBg: rgba(255, 255, 255, 0.4);


// ----------------- 浅色主题色板 -------------------
// 基础色板
@colorPrimary1: #EEF6E8;  // 主色1
@colorPrimary2: #C8D9B6;  // 主色2
@colorPrimary3: #AACC8B;  // 主色3
@colorPrimary4: #8DBF63;  // 主色4
@colorPrimary5: #68B92E;  // 主色5
@colorPrimary6: #55A722;  // 主色6
@colorPrimary7: #398013;  // 主色7
@colorPrimary8: #225909;  // 主色8
@colorPrimary9: #103303;  // 主色9
@colorPrimary10: #040D01;  // 主色10

// 主色板
@colorPrimary: @colorPrimary6;  // 主色
@colorPrimaryHover: @colorPrimary5;  // 主色悬浮
@colorPrimaryClick: @colorPrimary7;  // 主色点击
@colorPrimaryActive: @colorPrimary1;  // 主色点击  选中状态（ #55A722-10%） #EEF6E8

// 中性色板
@colorBg1: #D6D7D9;  // 二级背景色
@colorBg2: #DFE0E1;  // 图表悬浮色
@colorBg3: #E7E8EA;  // 表头背景色
@colorBg4: #F0F1F3;  // 悬浮色
@colorBg5: #F7F8FA;  // 滚动条
@colorLine1: #B4B6BB;  // 图表分割线
@colorLine2: #C2C4C7;  // 图表分割线（二级）
@colorLine3: #D0D2D5; // 组件描边
@colorLine4: #DEE0E3; // 组件描边
@colorBorder: #EBEEF2; // 组件描边

@loginBorder: @colorLine3; // 登录框文字的描边
@layoutContentBg: @colorBg5;  // 底色
// // @contentBg: #1c1c1e;  // 背景色
// @contentBorder: #48484a; // 线框描边, 登录框文字的描边
// @borderColor: #38383a; // 页面分割线
// @loginBg: rgba(255, 255, 255, 0.07); // 登录背景



// --------------------------- 黑暗主题 ---------------------------

// 基础色板
@darkPrimary1: #c6ccc0;  // 主色1
@darkPrimary2: #adbf99;  // 主色2
@darkPrimary3: #91b372;  // 主色3
@darkPrimary4: #76a650;  // 主色4
@darkPrimary5: #5c9931;  // 主色5
@darkPrimary6: #438b16;  // 主色6
@darkPrimary7: #2b660b;  // 主色7
@darkPrimary8: #174004;  // 主色8
@darkPrimary9: #071a00;  // 主色9
@darkPrimary10: #000000;  // 主色10

// 主色板
@DarkcolorPrimary: @darkPrimary6;  // 主色
@darkColorPrimaryHover: @darkPrimary5;  // 主色悬浮
@darkColorPrimaryClick: @darkPrimary7;  // 主色点击
@darkColorPrimaryActive: rgba(67,139,22,0.5);  // 主色点击  选中状态（ #438B16-50%）

// 中性色板

// @darkColorNeutral2: #131314;  // 二级背景色
// @darkColorNeutral4: #28292b;  // 图表悬浮色
// @darkColorNeutral5: #2b2b2f;  // 表头背景色
// @darkColorNeutral6: #3a3a3c;  // 悬浮色
// @darkColorNeutral7: #79797d;  // 滚动条
// @darkColorNeutral8: #444447;  // 图表分割线
// @darkColorNeutral9: #2f2f31;  // 图表分割线（二级）
// @darkColorNeutral11: #272729; // 组件描边

@darkLayoutContentBg: @darkPrimary10;  // 底色
@darkContentBg: #1c1c1e;  // 背景色
@darkContentBorder: #48484a; // 线框描边, 登录框文字的描边
@darkBorderColor: #38383a; // 页面分割线
@darkLoginBg: rgba(255, 255, 255, 0.07); // 登录背景

// 字体颜色
@darkText85: rgba(255, 255, 255, 0.85);
@darkText65: rgba(255, 255, 255, 0.65);
@darkText55: rgba(255, 255, 255, 0.55);
@darkText35: rgba(255, 255, 255, 0.55);
@darkText25: rgba(255, 255, 255, 0.25);
@darkSvgColor: @darkText85;  // svg颜色

// 语义色板
// 表示明确的信息以及状态，比如成功、出错、失败、提醒、链接等。提供5种等级颜色，请结合实际业务使用。
@darkColorSuccess: @darkPrimary6;
@darkColorInfo: #1973d4;
@darkColorWarning: #db7e1a;
@darkColorError: #d95a50;
@darkColorDangerous: #8f0d0d;
// 分类色板
// 分类色板用于描述分类数据，常用一个颜色代表一个值以区分不同类型。取色时色相分布均衡，相邻颜色之间明暗需考虑差异性；
// 常用于饼图的不同分类、填充地图中的不同国家、关系图中的不同角色等。
@darkColorC1: #68b92e;
@darkColorC2: #5F96ea;
@darkColorC3: #6b69f2;
@darkColorC4: #D9aa43;
@darkColorC5: #65789b;
@darkColorC6: #62a7f2;
@darkColorC7: #18a8a7;
@darkColorC8: #ffaf29;
@darkColorC9: #ff5b3e;
@darkColorC10: #ba7de7;


// ---------------- 公安蓝色主题色板 ---------------
